import store from "store2";

// 邮箱类型
export type MailType = "Gmail" | "Outlook";

// 邮箱认证检查请求
export interface CheckEmailAuthRequest {
  email: string;
}

// 邮箱添加请求
export interface AddEmailRequest {
  projectId: string;
  type: MailType;
  sender: string;
  email: string;
  companyName: string;
  companyAddress: string;
  productPage: string;
  contactUs: string;
}

// 邮箱响应
export interface EmailResponse {
  id: string;
  projectId: string;
  type: MailType;
  sender: string;
  email: string;
  authorized: boolean;
  activated: boolean;
  companyName: string;
  companyAddress: string;
  productPage: string;
  contactUs: string;
  createTime: string;
}

// 获取邮箱列表请求
export interface GetEmailsRequest {
  projectId: string;
  pageNumber?: number;
  pageSize?: number;
}

// 邮箱列表响应
export interface EmailListResponse {
  items: EmailResponse[];
  total: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// 检查邮箱认证状态
export const checkEmailAuthorization = async (email: string): Promise<string> => {
  const response = await fetch(`/v1/emails/checkAuthorized?email=${encodeURIComponent(email)}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return await response.text();
};

// 添加邮箱
export const addEmail = async (emailData: AddEmailRequest): Promise<EmailResponse> => {
  const response = await fetch("/v1/emails", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(emailData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return await response.json();
};

// 获取邮箱列表
export const getEmails = async (request: GetEmailsRequest): Promise<EmailListResponse> => {
  const params = new URLSearchParams();
  params.append("projectId", request.projectId);
  
  if (request.pageNumber !== undefined) {
    params.append("pageNumber", request.pageNumber.toString());
  }
  if (request.pageSize !== undefined) {
    params.append("pageSize", request.pageSize.toString());
  }

  const response = await fetch(`/v1/emails?${params.toString()}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return await response.json();
};

// 删除邮箱
export const deleteEmail = async (emailId: string): Promise<void> => {
  const response = await fetch(`/v1/emails/${emailId}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

// 更新邮箱激活状态
export const updateEmailActivation = async (emailId: string, activated: boolean): Promise<EmailResponse> => {
  const response = await fetch(`/v1/emails/${emailId}/activation`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ activated }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return await response.json();
};
