import store from "store2";
import { responseCode } from "../types";

interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

interface UserProfile {
  id: string;
  username?: string;
  email?: string;
  oldPassword?: string;
  password?: string;
  phoneNo?: string | null;
  role: "admin" | "user";
  createTime?: string;
  updateTime?: string;
}

export const userLogin = async (data: LoginRequest) => {
  const response = await fetch(`/v1/token`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const userRegister = async (data: RegisterRequest) => {
  const response = await fetch(`/v1/users`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// Get current user profile
export const getUserProfile = async (): Promise<UserProfile> => {
  const response = await fetch("/v1/user", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// Update user profile
export const updateUserProfile = async (
  data: UserProfile
): Promise<UserProfile> => {
  const response = await fetch(`/v1/users/${data.id}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ ...data, username: data.username }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      (errorData.code && responseCode[errorData.code]) ||
        errorData.message ||
        `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// Delete user account with password verification
export const deleteUserAccount = async (
  id: string,
  password: string
): Promise<void> => {
  const response = await fetch(`/v1/users/${id}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ password }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

// 导出类型
export type { LoginRequest, RegisterRequest, UserProfile };
