import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Globe, AlertCircle } from "lucide-react";
import { getWidgetConfig } from "@/config";



interface ProjectSettings {
  logo: string;
  name: string;
  color: string;
  welcomeMsg: string;
  suggestedEnable: boolean;
  suggestedQuestions: string[];
}

interface Project {
  id: string;
  name: string;
  website: string;
  createTime: string;
  settings: ProjectSettings;
}

const PublicShare = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) {
        setError("ProjectNotFound");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // 使用widget配置获取项目信息
        const widgetConfig = getWidgetConfig();
        const response = await fetch(`${widgetConfig.api.projectsEndpoint}/${projectId}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const projectData = await response.json();
        setProject(projectData);
      } catch (err) {
        console.error("Project Load Error:", err);
        setError("ProjectNotFound");
      } finally {
        setLoading(false);
      }
    };

    loadProject();
  }, [projectId]);

  // 动态加载聊天窗口脚本
  useEffect(() => {
    if (project) {
      // 清理之前的脚本
      const existingScript = document.querySelector('script[project-id]');
      if (existingScript) {
        existingScript.remove();
      }

      // 创建新的脚本标签并添加到head
      const script = document.createElement('script');
      script.async = true;
      script.src = `${getWidgetConfig().api.baseUrl}/widget.js`;
      script.setAttribute('project-id', project.id);
      document.head.appendChild(script);

      // 清理函数
      return () => {
        const scriptToRemove = document.querySelector('script[project-id]');
        if (scriptToRemove) {
          scriptToRemove.remove();
        }
      };
    }
  }, [project]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Loading experience page...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h1>
          <p className="text-gray-600 mb-6">
            {error || "Page Not Found"}
          </p>
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Globe className="h-4 w-4 mr-2" />
            Back to Home
          </a>
        </div>
      </div>
    );
  }

  return (
      <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
        {/* 顶部信息栏 */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex-shrink-0">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {project.settings.logo && (
                  <img
                      src={project.settings.logo}
                      alt={project.settings.name}
                      className="h-8 w-8 rounded-full object-cover"
                  />
              )}
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  {project.settings.name || project.name}
                </h1>
                <p className="text-sm text-gray-600">Test your bot with real questions</p>
              </div>
            </div>
            <div className="group flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full group-hover:bg-blue-200 transition-colors duration-200">
                🌐
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-xs text-gray-500 uppercase tracking-wide font-semibold">Website</div>
                <a
                    href={project.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-900 hover:text-blue-600 truncate block transition-colors duration-200"
                >
                  {project.website}
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 relative">
          <iframe
              src={project.website}
              className="absolute inset-0 w-full h-full border-0"
              title="Website Preview"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          />
        </div>

        {/* 底部品牌信息 */}
        <div className="fixed bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>Powered by</span>
            <a
                href="/"
                className="font-semibold text-blue-600 hover:text-blue-700"
            >
              <img
                  src="/logo.png"
                  alt="YapYapBot Logo"
                  style={{
                    width: '16px',
                    height: '16px',
                    marginRight: '4px',
                    verticalAlign: 'middle',
                    display: 'inline-block'
                  }}
              />
              YapYapBot
            </a>
          </div>
        </div>
      </div>
  );
};

export default PublicShare;
