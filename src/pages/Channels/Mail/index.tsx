import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import { AddMailDialog } from "@/components/dialogs/AddMailDialog";
import { useTranslation } from "@/hooks/useTranslation";
import { useToast } from "@/hooks/use-toast";
import { useProject } from "@/context/ProjectContext";
import {
  getEmails,
  deleteEmail,
  updateEmailActivation,
  EmailResponse,
} from "@/services/api";
import { Mail, Plus, Edit, Trash2, Loader2 } from "lucide-react";
import { format } from "date-fns";

const MailManagement: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { currentProject } = useProject();

  // 状态管理
  const [emails, setEmails] = useState<EmailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<EmailResponse | null>(null);

  // 加载邮箱列表
  const loadEmails = async () => {
    if (!currentProject?.id) return;

    try {
      setLoading(true);
      const response = await getEmails({
        projectId: currentProject.id,
        pageNumber: 1,
        pageSize: 100,
      });
      setEmails(response.items);
    } catch (error) {
      console.error("Failed to load emails:", error);
      toast({
        title: t("error"),
        description: "Failed to load emails",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadEmails();
  }, [currentProject?.id]);

  // 处理激活状态切换
  const handleActivationToggle = async (email: EmailResponse, activated: boolean) => {
    try {
      await updateEmailActivation(email.id, activated);
      
      // 更新本地状态
      setEmails(prev => 
        prev.map(item => 
          item.id === email.id 
            ? { ...item, activated } 
            : item
        )
      );

      toast({
        title: t("success"),
        description: `Email ${activated ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error("Failed to update email activation:", error);
      toast({
        title: t("error"),
        description: "Failed to update email activation",
        variant: "destructive",
      });
    }
  };

  // 处理删除邮箱
  const handleDelete = async () => {
    if (!selectedEmail) return;

    try {
      await deleteEmail(selectedEmail.id);
      
      // 从列表中移除
      setEmails(prev => prev.filter(item => item.id !== selectedEmail.id));
      
      toast({
        title: t("success"),
        description: "Email deleted successfully",
      });
    } catch (error) {
      console.error("Failed to delete email:", error);
      toast({
        title: t("error"),
        description: "Failed to delete email",
        variant: "destructive",
      });
    } finally {
      setDeleteConfirmOpen(false);
      setSelectedEmail(null);
    }
  };

  // 打开删除确认对话框
  const openDeleteConfirm = (email: EmailResponse) => {
    setSelectedEmail(email);
    setDeleteConfirmOpen(true);
  };

  // 渲染状态徽章
  const renderStatusBadge = (email: EmailResponse) => {
    if (email.authorized) {
      return (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-green-600">{t("connected")}</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          <span className="text-sm text-red-600">{t("disconnected")}</span>
        </div>
      );
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "yyyy-MM-dd HH:mm:ss");
    } catch {
      return dateString;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题和添加按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Mail className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold">{t("mailManagement")}</h1>
          </div>
          <Button onClick={() => setAddDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            {t("addMail")}
          </Button>
        </div>

        {/* 邮箱列表 */}
        <Card>
          <CardHeader>
            <CardTitle>{t("mailManagement")}</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin mr-2" />
                <span>{t("loading")}</span>
              </div>
            ) : emails.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Mail className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No emails configured yet</p>
                <p className="text-sm">Click "Add Mail" to get started</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("activated")}</TableHead>
                    <TableHead>{t("type")}</TableHead>
                    <TableHead>{t("sender")}</TableHead>
                    <TableHead>{t("email")}</TableHead>
                    <TableHead>{t("status")}</TableHead>
                    <TableHead>{t("created")}</TableHead>
                    <TableHead className="text-right">{t("actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {emails.map((email) => (
                    <TableRow key={email.id}>
                      <TableCell>
                        <Switch
                          checked={email.activated}
                          onCheckedChange={(checked) => handleActivationToggle(email, checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{email.type}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">{email.sender}</TableCell>
                      <TableCell>{email.email}</TableCell>
                      <TableCell>{renderStatusBadge(email)}</TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatDate(email.createTime)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => openDeleteConfirm(email)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 添加邮箱对话框 */}
      <AddMailDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onSuccess={loadEmails}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirm
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        onConfirm={handleDelete}
        title="Delete Email"
        description={`Are you sure you want to delete the email "${selectedEmail?.email}"? This action cannot be undone.`}
      />
    </DashboardLayout>
  );
};

export default MailManagement;
