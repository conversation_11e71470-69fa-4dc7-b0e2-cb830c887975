import { ChatPreview } from "@/components/ChatPreview";
import ChatbotConfigForm from "@/components/ChatbotConfigForm";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { updateProject } from "@/services/api";
import { Bot, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ChatbotConfig } from "@/components/ChatbotConfigForm"; // 导入ChatbotConfig类型

const Project = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject, refreshProjects, setCurrentProject } = useProject();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { id } = useParams();

  // 直接显示配置页面，不显示项目列表
  const [loading, setLoading] = useState(true);

  // 使用单个ChatbotConfig对象存储所有配置
  const [chatbotConfig, setChatbotConfig] = useState<ChatbotConfig>({
    brandName: "YapYapBot",
    brandColor: "#9067bc",
    logo: "",
    welcomeMessage: t("defaultWelcomeMessage"),
    suggestedQuestionsEnabled: true,
    suggestedQuestions: [
      t("defaultSuggestedQuestion1"),
      t("defaultSuggestedQuestion2"),
      t("defaultSuggestedQuestion3"),
    ],
    formEnable: false,
    externalFormUrl: "",
    internalFormUrl: "",
    currentForm: 0,
    whatsappEnable: false,
    whatsappAddress: "",
    notificationEnabled: false,
    notificationEmails: [],
    customPrompt: "",
  });

  // 从当前项目加载配置
  useEffect(() => {
    if (currentProject) {
      // 从项目设置中加载配置，如果没有则保持默认值
      setChatbotConfig((prev) => ({
        ...prev,
        brandName:
          currentProject.settings?.name || currentProject.name || "YapYapBot",
        brandColor: currentProject.settings?.color || "#9067bc",
        logo: currentProject.settings?.logo || "",
        welcomeMessage:
          currentProject.settings?.welcomeMsg || t("defaultWelcomeMessage"),
        suggestedQuestionsEnabled:
          currentProject.settings?.suggestedEnable ?? true,
        suggestedQuestions: currentProject.settings?.suggestedQuestions || [
          t("defaultSuggestedQuestion1"),
          t("defaultSuggestedQuestion2"),
          t("defaultSuggestedQuestion3"),
        ],
        // 添加表单和 WhatsApp 设置的加载
        formEnable: (currentProject.settings as any)?.formEnable ?? false,
        externalFormUrl:
          (currentProject.settings as any)?.externalFormUrl || "",
        internalFormUrl:
          (currentProject.settings as any)?.internalFormUrl || "",
        currentForm: (currentProject.settings as any)?.currentForm ?? 0,
        whatsappEnable:
          (currentProject.settings as any)?.whatsappEnable ?? false,
        whatsappAddress:
          (currentProject.settings as any)?.whatsappAddress || "",
        notificationEnabled:
          (currentProject.settings as any)?.notificationEnabled ?? false,
        notificationEmails:
          (currentProject.settings as any)?.notificationEmails || [],
        customPrompt:
          (currentProject.settings as any)?.customPrompt || "",
      }));
      setLoading(false);
    }
  }, [currentProject]);

  // 认证检查
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleSave = async () => {
    try {
      // 优先使用路由参数的 id，如果没有则使用 currentProject.id
      const projectId = id || currentProject?.id;

      if (!projectId) {
        toast({
          variant: "destructive",
          title: t("error"),
          description: t("projectIdMissing"),
        });
        return;
      }

      const requestParams = {
        id: projectId,
        name: chatbotConfig.brandName,
        settings: {
          logo: chatbotConfig.logo,
          name: chatbotConfig.brandName,
          color: chatbotConfig.brandColor,
          welcomeMsg: chatbotConfig.welcomeMessage,
          suggestedEnable: chatbotConfig.suggestedQuestionsEnabled,
          suggestedQuestions: chatbotConfig.suggestedQuestions,
          // 添加表单和 WhatsApp 设置到保存参数中
          formEnable: chatbotConfig.formEnable,
          externalFormUrl: chatbotConfig.externalFormUrl,
          internalFormUrl: chatbotConfig.internalFormUrl,
          currentForm: chatbotConfig.currentForm,
          whatsappEnable: chatbotConfig.whatsappEnable,
          whatsappAddress: chatbotConfig.whatsappAddress,
          notificationEnabled: chatbotConfig.notificationEnabled,
          notificationEmails: chatbotConfig.notificationEmails,
          customPrompt: chatbotConfig.customPrompt,
          // displayQuicksetBranding: chatbotConfig.displayQuicksetBranding
        },
      };

      await updateProject(requestParams);
      setCurrentProject({ ...currentProject, ...requestParams });
      refreshProjects();
      toast({
        title: t("success"),
        description: t("saveConfig"),
      });
    } catch (error) {
      console.error("Failed to save project:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("saveFailed"),
      });
    }
  };

  // 创建更新配置的函数
  const updateConfig: <K extends keyof ChatbotConfig>(
    field: K,
    value: ChatbotConfig[K]
  ) => void = (field, value) => {
    setChatbotConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">{t("loading")}</div>
        </div>
      </Card>
    );
  }

  // 如果没有项目数据，显示错误信息
  if (!currentProject) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-gray-600 mb-4">
              {t("projectNotFound")}
            </div>
            <Button onClick={() => navigate("/projects")} variant="outline">
              {t("returnToProjectList")}
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  // 项目配置视图
  return (
    <div className="min-h-screen rounded-lg">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Bot className="mr-2 h-6 w-6" />
                {t("appearance")} - {currentProject.name}
              </h1>
              <p className="text-gray-600 mt-1">{t("adjustAppearance")}</p>
            </div>
          </div>
          <Button
            onClick={handleSave}
            className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
          >
            <Save className="w-4 h-4 group-hover:scale-110 transition-transform" />
            {t("save")}
          </Button>
        </div>
      </div>
      {/* 主要内容区域 */}
      <div className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：配置表单 */}
            <div className="space-y-6">
              <ChatbotConfigForm
                config={chatbotConfig}
                updateConfig={updateConfig}
              />
            </div>

            {/* 右侧：聊天预览 */}
            <div className="flex justify-center">
              <div className="w-full max-w-sm">
                <div
                  className="bg-white rounded-lg shadow-lg overflow-hidden"
                  style={{ width: "350px", height: "600px" }}
                >
                  <ChatPreview
                    assistantName={chatbotConfig.brandName || "YapYapBot"}
                    welcomeMessage={
                      chatbotConfig.welcomeMessage ||
                      `Welcome! 👋\nI'm YapYapBot, here to assist with any questions you have. How can I help you today?`
                    }
                    color={chatbotConfig.brandColor}
                    suggestedQuestions={
                      chatbotConfig.suggestedQuestionsEnabled
                        ? chatbotConfig.suggestedQuestions
                        : []
                    }
                    showInput={true}
                    compact={false}
                    className="h-full"
                    logo={chatbotConfig.logo}
                    projectId={currentProject?.id}
                    trackUrls={[
                      chatbotConfig.internalFormUrl || "",
                      chatbotConfig.externalFormUrl || "",
                      chatbotConfig.whatsappAddress || "",
                    ]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Project;
