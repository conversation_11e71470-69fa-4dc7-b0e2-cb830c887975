import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProductInfo } from "@/components/ProductInfo";
import { supportMail } from "@/config";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useTranslation } from "@/hooks/useTranslation";
import { DashboardStatistics, statisticsDashboard } from "@/services/api";
import {
  Clock,
  ExternalLink,
  FileText,
  MessageSquare,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

// 创建数据源的默认值
const defaultDashboardData: DashboardStatistics = {
  overview: {
    totalConversations: 0,
    totalKnowledge: 0,
    totalWebsites: 0,
    totalDocuments: 0,
    conversations: [],
  },
  latestConversations: [],
  topLocations: [],
  topChannels: [],
};

const Dashboard = () => {
  const { t } = useTranslation();
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const [selectedPeriod, setSelectedPeriod] = useState("7");
  const [dashboardData, setDashboardData] =
    useState<DashboardStatistics>(defaultDashboardData);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (currentProject?.id) {
      loadData(Number(selectedPeriod));
    }
  }, [currentProject]);

  // 根据当前时间生成问候语
  const getGreeting = () => {
    const hour = new Date().getHours();

    if (hour >= 5 && hour < 9) {
      return "Good morning"; // Good morning (5:00 - 8:59)
    } else if (hour >= 9 && hour < 12) {
      return "Good morning"; // Good morning (9:00 - 11:59)
    } else if (hour >= 12 && hour < 17) {
      return "Good afternoon"; // Good afternoon (14:00 - 16:59)
    } else if (hour >= 17 && hour < 22) {
      return "Good evening"; // Good evening (17:00 - 21:59)
    } else if (hour >= 22 || hour < 1) {
      return "Late night"; // Late night (22:00 - 0:59)
    } else {
      return "Very late night"; // Very late night (1:00 - 4:59)
    }
  };

  // 格式化相对时间
  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return "now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      // 超过7天显示具体日期
      return date.toLocaleDateString("zh-CN", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    }
  };

  function onDateRangeChange(v: string) {
    setSelectedPeriod(v);
    loadData(Number(v));
  }

  function loadData(passDay: number) {
    const startTime = new Date();
    startTime.setDate(startTime.getDate() - passDay);
    const endTime = new Date();
    statisticsDashboard(
      currentProject?.id,
      startTime.toISOString(),
      endTime.toISOString()
    ).then((data) => {
      setDashboardData(data);
    });
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {getGreeting()}
              <Clock className="w-5 h-5 text-gray-500" />
            </h1>
            <p className="text-gray-600 mt-1">
              {t("dashboardOverview")}
            </p>
          </div>
        </div>

        {/* Current Product Info */}
        <ProductInfo variant="dashboard" />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Section */}
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">
                    Overview
                  </CardTitle>
                  <Select
                    value={selectedPeriod}
                    onValueChange={(v) => onDateRangeChange(v)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">Last 7 days</SelectItem>
                      <SelectItem value="30">Last 30 days</SelectItem>
                      <SelectItem value="90">Last 90 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <p className="text-sm text-gray-600">
                  Performance insights for the selected period
                </p>
              </CardHeader>
              <CardContent>
                {/* Metrics Row */}
                <div className="grid grid-cols-4 gap-6 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      {dashboardData.overview.totalConversations}
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("totalConversations")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      {dashboardData.overview.totalKnowledge}
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("totalKnowledge")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      {dashboardData.overview.totalWebsites}
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("totalWebsites")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      {dashboardData.overview.totalDocuments}
                    </div>
                    <div className="text-sm text-gray-600">
                      {t("totalDocuments")}
                    </div>
                  </div>
                </div>

                {/* Chart */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-4">
                    New conversations by day
                  </h4>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={dashboardData.overview.conversations}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="date"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: "#666" }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: "#666" }}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: "white",
                            border: "1px solid #e5e7eb",
                            borderRadius: "8px",
                            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="count"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.1}
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bottom Row - Top Locations and Channels */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Top Locations */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold">
                    Top locations
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Countries your customers come from
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboardData.topLocations.map((location, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-3">
                          {/* <span className="text-lg">{location.flag}</span> */}
                          <span className="text-sm font-medium">
                            {location.country}
                          </span>
                        </div>
                        <span className="text-sm font-semibold">
                          {location.count}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Channels */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold">
                    Top channels
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Channels your customers prefer
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboardData.topChannels.map((channel, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            {channel.channel}
                          </span>
                        </div>
                        <span className="text-sm font-semibold">
                          {channel.count}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Latest Conversations */}
            <Card className="shadow-sm border-gray-200">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-semibold">
                    Latest Conversations
                  </CardTitle>
                  <Badge
                    variant="secondary"
                    className="bg-blue-100 text-blue-800"
                  >
                    NEW
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {dashboardData.latestConversations.length === 0 ? (
                  <div className="p-6 text-center">
                    <MessageSquare className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">
                      No more conversations
                    </p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-100">
                    {dashboardData.latestConversations.map(
                      (conversation, index) => (
                        <div
                          key={conversation.id || index}
                          className="p-4 hover:bg-gray-50 transition-colors duration-200 cursor-pointer group"
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                                <p className="flex-grow truncate text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                  {conversation.name || "Unknown Conversation"}
                                </p>
                                <span className="flex items-center gap-1 text-xs text-gray-500 flex-shrink-0">
                                  <Clock className="w-3 h-3" />
                                  {formatRelativeTime(conversation.createTime)}
                                </span>
                              </div>
                            </div>
                          </div>

                        </div>
                      )
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Live Chat */}
            {/* <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-semibold">Live chat</CardTitle>
                <p className="text-sm text-gray-600">
                  Connect a helpdesk to enable live agent support.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant="outline" className="text-gray-600">
                      {liveChatData.status}
                    </Badge>
                  </div>

                  {liveChatData.agents.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-xs font-medium">Z</span>
                        </div>
                        <span className="text-sm font-medium">{agent.name}</span>
                      </div>
                      <Button variant="outline" size="sm">
                        {agent.status}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card> */}

            {/* Help Section */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-semibold">Help</CardTitle>
                <p className="text-sm text-gray-600">
                  Get help straight from the team at Outpost.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <a target="_blank" href="/documentation">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      size="sm"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Documentation
                      <ExternalLink className="w-4 h-4 ml-auto" />
                    </Button>
                  </a>
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 mb-3">
                    We're here for you! If you have any questions or need
                    assistance, feel free to reach out to us at{" "}
                    <Button variant="link" className="p-0 h-auto text-blue-600">
                      {supportMail}
                    </Button>
                  </p>
                  <a href={`mailto:${supportMail}`} title="email">
                    <Button variant="outline" size="sm" className="w-full">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Contact Support
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
