import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import { UploadFileDialog } from "@/components/dialogs/UploadFileDialog";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { getKnowledges, uploadFile, KnowledgeResponse } from "@/services/api";
import { FileText, Filter, Loader2, Search, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { KnowledgePagination } from "./KnowledgePagination";
import {deleteKnowledgeFile} from "@/services/api/knowledge.ts";

interface DocumentTabProps {
  projectId: string;
  onStatisticsUpdate: () => void;
}

export const DocumentTab = ({
  projectId,
  onStatisticsUpdate,
}: DocumentTabProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Document相关状态
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDocumentDialog, setShowDocumentDialog] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 分页和搜索状态
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  // 获取知识库数据
  const fetchKnowledges = async (page: number = currentPage) => {
    if (!projectId) return;

    setLoading(true);
    try {
      const knowledgeResponse = await getKnowledges({
        projectId: projectId,
        type: "Document",
        pageNumber: page,
        pageSize: pageSize,
      });
      setKnowledgeItems(knowledgeResponse.items || []);
      setTotalItems(knowledgeResponse.total || 0);
    } catch (error) {
      console.error("Failed to fetch knowledges:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("failedToLoadKnowledge"),
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchKnowledges();
    }
  }, [projectId, currentPage, searchQuery]);

  // 处理搜索变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };


  // 处理文件上传
  const handleFileUpload = async (files: FileList) => {
    if (!projectId) return;

    setUploading(true);
    try {
      await uploadFile({
        projectId: projectId,
        files: files,
      });

      toast({
        title: t("success"),
        description: t("documentUploaded"),
      });

      // 重新获取知识库数据和统计信息
      fetchKnowledges(1);
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to upload document:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("uploadFailed"),
      });
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteItem = async (id: string) => {
    await deleteKnowledgeFile(id);
    setKnowledgeItems((prev) => prev.filter((item) => item.id !== id));
    // 重新获取数据和统计信息以更新总数
    fetchKnowledges(currentPage);
    onStatisticsUpdate();
  };

  // 过滤出文档类型的知识库项目
  const documentItems = knowledgeItems.filter(
    (item) => item.type === "Document"
  );

  return (
    <div className="space-y-6">
      {/* Document操作按钮 */}
      <div className="flex gap-4">
        <Button
            className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
          onClick={() => setShowDocumentDialog(true)}
        >
          <FileText className="w-4 h-4" />
          <span>{t("uploadDocument")}</span>
        </Button>
      </div>

      {/* Document搜索 */}
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("search")}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" className="flex items-center space-x-2">
          <Filter className="w-4 h-4" />
          <span>{t("filter")}</span>
        </Button>
      </div>

      {/* Document表格 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="w-8 h-8 text-gray-400 animate-spin mb-4" />
              <p className="text-gray-600">{t("loading")}</p>
            </div>
          ) : documentItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No documents found
              </h3>
              <p className="text-gray-600 mb-4">
                Start by uploading your first document
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("title")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("status")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("lastTrained")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("actions")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {documentItems.map((item) => (
                    <tr key={item.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gray-100 rounded">
                            <FileText className="w-4 h-4 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {item.name}
                            </p>
                            <p className="text-sm text-gray-500">
                              {`${(parseInt(item.size) / 1024).toFixed(1)} KB`}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge
                          variant="default"
                          className={
                            item.status === "Parsed"
                              ? "bg-green-100 text-green-800 border-green-200"
                              : item.status === "Parsing"
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : "bg-red-100 text-red-800 border-red-200"
                          }
                        >
                          <span
                            className={`w-2 h-2 rounded-full mr-2 ${
                              item.status === "Parsed"
                                ? "bg-green-500"
                                : item.status === "Parsing"
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            }`}
                          ></span>
                          {item.status}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">
                          {new Date(item.createTime).toLocaleString()}
                        </span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <DeleteConfirm
                            title="Delete Document"
                            description="Are you sure you want to delete this document from the knowledge base? This action cannot be undone."
                            tooltipContent={t("deleteItem")}
                            onConfirm={() => handleDeleteItem(item.id)}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </DeleteConfirm>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 上传文档对话框 */}
      <UploadFileDialog
        open={showDocumentDialog}
        onOpenChange={setShowDocumentDialog}
        onUpload={handleFileUpload}
        uploading={uploading}
      />

      {/* 分页组件 */}
      <KnowledgePagination
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
