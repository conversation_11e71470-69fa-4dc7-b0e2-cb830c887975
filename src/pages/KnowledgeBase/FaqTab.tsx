import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  createFaq,
  deleteFaq,
  FaqResponse,
  getFaqs,
  updateFaq,
} from "@/services/api";
import {
  Download,
  Edit, ExternalLink,
  FileText,
  Loader2,
  MessageCircleQuestion,
  Plus,
  Search,
  Trash2,
} from "lucide-react";
import { useEffect, useState } from "react";
import { KnowledgePagination } from "./KnowledgePagination";
import { UploadFileDialog } from "@/components/dialogs/UploadFileDialog";
import { uploadFaqs } from "@/services/api/knowledge";

interface FaqTabProps {
  projectId: string;
  onStatisticsUpdate: () => void;
}

export const FaqTab = ({ projectId, onStatisticsUpdate }: FaqTabProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // FAQ相关状态
  const [faqItems, setFaqItems] = useState<FaqResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FaqResponse | null>(null);
  const [faqQuestion, setFaqQuestion] = useState("");
  const [faqAnswer, setFaqAnswer] = useState("");
  const [faqLoading, setFaqLoading] = useState(false);

  // 分页和搜索状态
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  // 下载状态
  const [downloading, setDownloading] = useState(false);

  const [showFileDialog, setShowFileDialog] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 获取FAQ数据
  const fetchFaqs = async (page: number = currentPage) => {
    if (!projectId) return;

    setLoading(true);
    try {
      const response = await getFaqs({
        projectId: projectId,
        pageNumber: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchQuery && { keyword: searchQuery }),
      });

      if (Array.isArray(response)) {
        setFaqItems(response);
        setTotalItems(response.length);
      } else {
        setFaqItems(response.items || []);
        setTotalItems(response.total || 0);
      }
    } catch (error) {
      console.error("Failed to fetch FAQs:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to load FAQs",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchFaqs();
    }
  }, [projectId, currentPage, searchQuery]);

  // 处理搜索变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // FAQ相关操作
  const handleAddFaq = () => {
    setEditingFaq(null);
    setFaqQuestion("");
    setFaqAnswer("");
    setShowFaqDialog(true);
  };

  const handleEditFaq = (faq: FaqResponse) => {
    setEditingFaq(faq);
    setFaqQuestion(faq.question);
    setFaqAnswer(faq.answer);
    setShowFaqDialog(true);
  };

  // 处理文件上传
  const handleFileUpload = async (files: FileList) => {
    if (!projectId) return;

    setUploading(true);
    try {
      await uploadFaqs({
        projectId: projectId,
        files: files,
      });
      toast({
        title: t("success"),
        description: t("documentUploaded"),
      });

      // 重新获取知识库数据和统计信息
      fetchFaqs();

      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to upload document:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("uploadFailed"),
      });
    } finally {
      setUploading(false);
    }
  };

  const handleSaveFaq = async () => {
    if (!projectId || !faqQuestion.trim() || !faqAnswer.trim()) return;

    setFaqLoading(true);
    try {
      if (editingFaq) {
        // 更新FAQ
        const updatedFaq = await updateFaq(editingFaq.id, {
          question: faqQuestion,
          answer: faqAnswer,
        });
        setFaqItems((prev) =>
          prev.map((item) => (item.id === editingFaq.id ? updatedFaq : item))
        );
        toast({
          title: t("success"),
          description: "FAQ updated successfully",
        });
      } else {
        // 创建新FAQ
        const newFaq = await createFaq({
          projectId: projectId,
          question: faqQuestion,
          answer: faqAnswer,
        });
        setFaqItems((prev) => [newFaq, ...prev]);
        toast({
          title: t("success"),
          description: "FAQ created successfully",
        });
      }

      setShowFaqDialog(false);
      setFaqQuestion("");
      setFaqAnswer("");
      setEditingFaq(null);
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to save FAQ:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to save FAQ",
      });
    } finally {
      setFaqLoading(false);
    }
  };

  const handleDeleteFaq = async (faqId: string) => {
    try {
      await deleteFaq(faqId);
      setFaqItems((prev) => prev.filter((item) => item.id !== faqId));
      toast({
        title: t("success"),
        description: "FAQ deleted successfully",
      });
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to delete FAQ:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to delete FAQ",
      });
    }
  };

  // 处理文件下载
  const handleDownloadTemplate = () => {
    setDownloading(true);
    try {
      const templateURL =
        "https://ai-chatbot-sg.oss-ap-southeast-1.aliyuncs.com/system/FAQ-Template.xlsx";
      const link = document.createElement("a");
      link.href = templateURL;
      link.download = "FAQ-Template.xlsx";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Failed to download template:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("downloadFailed"),
      });
    } finally {
      setDownloading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* FAQ操作按钮 */}
      <div className="flex gap-4 items-end">
        <Button
          className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
          onClick={handleAddFaq}
        >
          <Plus className="w-4 h-4" />
          <span>Add FAQ</span>
        </Button>
        <Button
          className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
          onClick={() => setShowFileDialog(true)}
        >
          <FileText className="w-4 h-4" />
          <span>{t("uploadFaqs")}</span>
        </Button>
        <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadTemplate}
            className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
        >
          {t("downloadTemplate")}
          <Download className="w-4 h-4 ml-1" />
        </Button>
      </div>

      {/* FAQ搜索 */}
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("search")}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* FAQ表格 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="w-8 h-8 text-gray-400 animate-spin mb-4" />
              <p className="text-gray-600">{t("loading")}</p>
            </div>
          ) : faqItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <MessageCircleQuestion className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No FAQs found
              </h3>
              <p className="text-gray-600 mb-4">
                Start by adding your first FAQ
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-600 w-[30%]">
                      {t("question")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[40%]">
                      {t("answer")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[15%]">
                      {t("created")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[15%]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {faqItems.map((faq) => (
                    <tr key={faq.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 w-[30%]">
                        <div className="text-sm text-gray-900">
                          {faq.question}
                        </div>
                      </td>
                      <td className="p-4 w-[40%]">
                        <div className="text-sm text-gray-600">
                          {faq.answer}
                        </div>
                      </td>
                      <td className="p-4 w-[15%]">
                        <span className="text-sm text-gray-600">
                          {new Date(faq.createTime).toLocaleString()}
                        </span>
                      </td>
                      <td className="p-4 w-[15%]">
                        <div className="flex items-center space-x-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditFaq(faq)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Edit FAQ</p>
                            </TooltipContent>
                          </Tooltip>
                          <DeleteConfirm
                            title="Delete FAQ"
                            description="Are you sure you want to delete this FAQ? This action cannot be undone."
                            tooltipContent="Delete FAQ"
                            onConfirm={() => handleDeleteFaq(faq.id)}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </DeleteConfirm>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 上传文档对话框 */}
      <UploadFileDialog
        title={t("uploadFaqs")}
        open={showFileDialog}
        accept=".xlsx"
        onOpenChange={setShowFileDialog}
        onUpload={handleFileUpload}
        uploading={uploading}
      />

      {/* FAQ对话框 */}
      <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{editingFaq ? "Edit FAQ" : "Add FAQ"}</DialogTitle>
            <DialogDescription>
              {editingFaq
                ? "Update the FAQ question and answer"
                : "Create a new FAQ with question and answer"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="faq-question">{t("question")}</Label>
              <Input
                id="faq-question"
                placeholder="Enter the question..."
                value={faqQuestion}
                onChange={(e) => setFaqQuestion(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="faq-answer">{t("answer")}</Label>
              <textarea
                id="faq-answer"
                placeholder="Enter the answer..."
                value={faqAnswer}
                onChange={(e) => setFaqAnswer(e.target.value)}
                className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowFaqDialog(false);
                setFaqQuestion("");
                setFaqAnswer("");
                setEditingFaq(null);
              }}
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSaveFaq}
              disabled={!faqQuestion.trim() || !faqAnswer.trim() || faqLoading}
            >
              {faqLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {editingFaq ? "Updating..." : "Creating..."}
                </>
              ) : editingFaq ? (
                "Update FAQ"
              ) : (
                "Create FAQ"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分页组件 */}
      <KnowledgePagination
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
