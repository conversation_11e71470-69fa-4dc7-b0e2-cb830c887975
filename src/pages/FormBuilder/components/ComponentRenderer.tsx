import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Upload } from "lucide-react";
import { format } from "date-fns";
import { useState } from "react";
import { FormComponent } from "../types/form-types";
import { useTranslation } from "@/hooks/useTranslation";

interface ComponentRendererProps {
    component: FormComponent;
    isPreview?: boolean;
    onUpdate?: (component: FormComponent) => void;
    value?: any;
    onChange?: (value: any) => void;
}

export const ComponentRenderer = ({
                                      component,
                                      isPreview = false,
                                      onUpdate,
                                      value,
                                      onChange,
                                  }: ComponentRendererProps) => {
    const [dateValue, setDateValue] = useState<Date>();
    const { t } = useTranslation();

    const handleValueChange = (newValue: any) => {
        if (onChange) {
            onChange(newValue);
        }
    };

    const renderLabel = () => {
        if (!component.label && component.type !== "divider" && component.type !== "label") return null;

        return (
            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                {component.label}
                {component.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
        );
    };

    const renderComponent = () => {
        switch (component.type) {
            case "label": {
                const { bold = false, fontSize = 'medium' } = component.properties || {};

                const sizeClasses = {
                    small: 'text-sm',
                    medium: 'text-base',
                    large: 'text-lg',
                    'extra-large': 'text-xl'
                };

                const sizeClass = sizeClasses[fontSize as keyof typeof sizeClasses] || 'text-base';
                const fontWeight = bold ? 'font-bold' : 'font-normal';

                return (
                    <span className={`text-gray-900 ${sizeClass} ${fontWeight}`}>
            {component.label}
          </span>
                );
            }

            case "text-input":
                return (
                    <Input
                        placeholder={component.placeholder}
                        value={value || ""}
                        onChange={(e) => handleValueChange(e.target.value)}
                        disabled={!isPreview}
                    />
                );

            case "textarea":
                return (
                    <Textarea
                        placeholder={component.placeholder}
                        value={value || ""}
                        onChange={(e) => handleValueChange(e.target.value)}
                        disabled={!isPreview}
                        rows={4}
                    />
                );

            case "select":
                return (
                    <Select
                        value={value}
                        onValueChange={handleValueChange}
                        disabled={!isPreview}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder={component.placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                            {component.options?.map((option) => (
                                <SelectItem key={option.value} value={String(option.value)}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                );

            case "radio":
                return (
                    <RadioGroup
                        value={value}
                        onValueChange={handleValueChange}
                        disabled={!isPreview}
                    >
                        {component.options?.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                                <RadioGroupItem
                                    value={String(option.value)}
                                    id={`${component.id}-${option.value}`}
                                />
                                <Label htmlFor={`${component.id}-${option.value}`}>
                                    {option.label}
                                </Label>
                            </div>
                        ))}
                    </RadioGroup>
                );

            case "checkbox":
                return (
                    <div className="space-y-2">
                        {component.options?.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                                <Checkbox
                                    id={`${component.id}-${option.value}`}
                                    checked={
                                        Array.isArray(value) ? value.includes(option.value) : false
                                    }
                                    onCheckedChange={(checked) => {
                                        if (!isPreview) return;
                                        const currentValues = Array.isArray(value) ? value : [];
                                        if (checked) {
                                            handleValueChange([...currentValues, option.value]);
                                        } else {
                                            handleValueChange(
                                                currentValues.filter((v) => v !== option.value)
                                            );
                                        }
                                    }}
                                    disabled={!isPreview}
                                />
                                <Label htmlFor={`${component.id}-${option.value}`}>
                                    {option.label}
                                </Label>
                            </div>
                        ))}
                    </div>
                );

            case "date-picker":
                return (
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                                disabled={!isPreview}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {dateValue ? format(dateValue, "PPP") : component.placeholder}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar
                                mode="single"
                                selected={dateValue}
                                onSelect={(date) => {
                                    setDateValue(date);
                                    handleValueChange(date);
                                }}
                                initialFocus
                            />
                        </PopoverContent>
                    </Popover>
                );

            case "number-input":
                return (
                    <Input
                        type="number"
                        placeholder={component.placeholder}
                        value={value || ""}
                        onChange={(e) => handleValueChange(Number(e.target.value))}
                        disabled={!isPreview}
                    />
                );

            case "email-input":
                return (
                    <Input
                        type="email"
                        placeholder={component.placeholder}
                        value={value || ""}
                        onChange={(e) => handleValueChange(e.target.value)}
                        disabled={!isPreview}
                    />
                );

            case "phone-input":
                return (
                    <Input
                        type="tel"
                        placeholder={component.placeholder}
                        value={value || ""}
                        onChange={(e) => handleValueChange(e.target.value)}
                        disabled={!isPreview}
                    />
                );

            case "file-upload":
                return (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="mt-4">
                            <Button variant="outline" disabled={!isPreview}>
                                {t("selectFile")}
                            </Button>
                            <p className="mt-2 text-sm text-gray-500">
                                {component.placeholder || t("dragAndDropSupport")}
                            </p>
                        </div>
                    </div>
                );

            case "heading": {
                const HeadingTag = `h${
                    component.properties?.level || 1
                }` as keyof JSX.IntrinsicElements;
                return (
                    <HeadingTag className="font-bold text-gray-900">
                        {component.label}
                    </HeadingTag>
                );
            }

            case "paragraph":
                return (
                    <p className="text-gray-700 leading-relaxed">{component.label}</p>
                );

            case "divider":
                return <hr className="border-gray-300" />;

            case "button":
                return (
                    <Button
                        variant={
                            component.properties?.variant === "secondary"
                                ? "secondary"
                                : "default"
                        }
                        disabled={!isPreview}
                    >
                        {component.label}
                    </Button>
                );

            // 复用组件
            case "full-name": {
                // value 结构: { lastName, firstName }
                const fullNameValue =
                    typeof value === "object" && value !== null ? value : {};
                return (
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("lastName")}
                            </Label>
                            <Input
                                placeholder={t("lastName")}
                                disabled={!isPreview}
                                value={fullNameValue["Last Name"] || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...fullNameValue,
                                        "Last Name": e.target.value,
                                    })
                                }
                            />
                        </div>
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("firstName")}
                            </Label>
                            <Input
                                placeholder={t("firstName")}
                                disabled={!isPreview}
                                value={fullNameValue["First Name"] || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...fullNameValue,
                                        "First Name": e.target.value,
                                    })
                                }
                            />
                        </div>
                    </div>
                );
            }
            case "contact-info": {
                // value 结构: { phone, email }
                const contactInfoValue =
                    typeof value === "object" && value !== null ? value : {};
                return (
                    <div className="space-y-4">
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("phoneNumber")}
                            </Label>
                            <Input
                                type="tel"
                                placeholder={t("enterPhone")}
                                disabled={!isPreview}
                                value={contactInfoValue.phone || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...contactInfoValue,
                                        phone: e.target.value,
                                    })
                                }
                            />
                        </div>
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("emailAddressLabel")}
                            </Label>
                            <Input
                                type="email"
                                placeholder={t("enterEmail")}
                                disabled={!isPreview}
                                value={contactInfoValue.email || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...contactInfoValue,
                                        email: e.target.value,
                                    })
                                }
                            />
                        </div>
                    </div>
                );
            }
            case "bank-card": {
                // value 结构: { cardNumber, bankName }
                const bankCardValue =
                    typeof value === "object" && value !== null ? value : {};
                return (
                    <div className="space-y-4">
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("bankCardNumber")}
                            </Label>
                            <Input
                                placeholder={t("enterBankCardNumber")}
                                disabled={!isPreview}
                                value={bankCardValue.cardNumber || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...bankCardValue,
                                        cardNumber: e.target.value,
                                    })
                                }
                            />
                        </div>
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("bankName")}
                            </Label>
                            <Input
                                placeholder={t("enterBankName")}
                                disabled={!isPreview}
                                value={bankCardValue.bankName || ""}
                                onChange={(e) =>
                                    handleValueChange({
                                        ...bankCardValue,
                                        bankName: e.target.value,
                                    })
                                }
                            />
                        </div>
                    </div>
                );
            }

            case "address": {
                // value 结构: { street, city, state, postalCode, country }
                const addressValue =
                    typeof value === "object" && value !== null ? value : {};
                return (
                    <div className="space-y-4">
                        <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                {t("streetAddress")}
                            </Label>
                            <Input
                                placeholder={t("enterStreetAddress")}
                                disabled={!isPreview}
                                value={addressValue.Street || ""}
                                onChange={(e) =>
                                    handleValueChange({ ...addressValue, Street: e.target.value })
                                }
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                    {t("city")}
                                </Label>
                                <Input
                                    placeholder={t("enterCity")}
                                    disabled={!isPreview}
                                    value={addressValue.City || ""}
                                    onChange={(e) =>
                                        handleValueChange({ ...addressValue, City: e.target.value })
                                    }
                                />
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                    {t("stateProvince")}
                                </Label>
                                <Input
                                    placeholder={t("enterStateProvince")}
                                    disabled={!isPreview}
                                    value={addressValue.State || ""}
                                    onChange={(e) =>
                                        handleValueChange({
                                            ...addressValue,
                                            State: e.target.value,
                                        })
                                    }
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                    {t("country")}
                                </Label>
                                <Input
                                    placeholder={t("enterCountry")}
                                    disabled={!isPreview}
                                    value={addressValue.Country || ""}
                                    onChange={(e) =>
                                        handleValueChange({
                                            ...addressValue,
                                            Country: e.target.value,
                                        })
                                    }
                                />
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                    {t("postalCode")}
                                </Label>
                                <Input
                                    placeholder={t("enterPostalCode")}
                                    disabled={!isPreview}
                                    value={addressValue.Code || ""}
                                    onChange={(e) =>
                                        handleValueChange({ ...addressValue, Code: e.target.value })
                                    }
                                />
                            </div>
                        </div>
                    </div>
                );
            }

            case "id-number":
                return (
                    <Input
                        placeholder={component.placeholder}
                        maxLength={18}
                        disabled={!isPreview}
                    />
                );

            default:
                return (
                    <div className="p-4 border border-gray-300 rounded bg-gray-50">
                        <p className="text-gray-500">未知组件类型: {component.type}</p>
                    </div>
                );
        }
    };

    return (
        <div className="space-y-2">
            {component.type !== "label" && renderLabel()}
            {renderComponent()}
        </div>
    );
};