import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useTranslation } from "@/hooks/useTranslation";
import { Plus, Settings, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { ComponentOption, FormComponent } from "../types/form-types";

interface PropertyPanelProps {
  selectedComponent: FormComponent | null;
  onUpdateComponent: (component: FormComponent) => void;
}

export const PropertyPanel = ({
  selectedComponent,
  onUpdateComponent,
}: PropertyPanelProps) => {
  const [localComponent, setLocalComponent] = useState<FormComponent | null>(
    null
  );
  const { t } = useTranslation();

  useEffect(() => {
    setLocalComponent(selectedComponent);
  }, [selectedComponent]);

  if (!localComponent) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("selectComponent")}
          </h3>
          <p className="text-gray-600">{t("clickComponentToEdit")}</p>
        </div>
      </div>
    );
  }

  const updateProperty = (key: string, value: any) => {
    const updatedComponent = { ...localComponent, [key]: value };
    setLocalComponent(updatedComponent);
    onUpdateComponent(updatedComponent);
  };

  const updateNestedProperty = (parentKey: string, key: string, value: any) => {
    const updatedComponent = {
      ...localComponent,
      [parentKey]: {
        ...localComponent[parentKey as keyof FormComponent],
        [key]: value,
      },
    };
    setLocalComponent(updatedComponent);
    onUpdateComponent(updatedComponent);
  };

  const addOption = () => {
    const newOption: ComponentOption = {
      label: `${t("option")}${(localComponent.options?.length || 0) + 1}`,
      value: `option${(localComponent.options?.length || 0) + 1}`,
    };
    const updatedOptions = [...(localComponent.options || []), newOption];
    updateProperty("options", updatedOptions);
  };

  const updateOption = (index: number, value: string) => {
    const updatedOptions = [...(localComponent.options || [])];
    updatedOptions[index] = { ...updatedOptions[index], label: value, value };
    updateProperty("options", updatedOptions);
  };

  const removeOption = (index: number) => {
    const updatedOptions = (localComponent.options || []).filter(
      (_, i) => i !== index
    );
    updateProperty("options", updatedOptions);
  };

  const hasOptions = ["select", "radio", "checkbox"].includes(
    localComponent.type
  );
  const hasPlaceholder = [
    "text-input",
    "textarea",
    "select",
    "date-picker",
    "number-input",
    "email-input",
    "phone-input",
    "file-upload",
    "id-number",
  ].includes(localComponent.type);
  const hasHeadingLevel = localComponent.type === "heading";
  const hasButtonVariant = localComponent.type === "button";

  return (
    <div className="h-full overflow-y-auto">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Settings className="w-5 h-5" />
          {t("propertySettings")}
        </CardTitle>
        <Badge variant="outline" className="w-fit">
          {localComponent.type}
        </Badge>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 基础属性 */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">{t("basicProperties")}</h4>

          <div className="space-y-2">
            <Label htmlFor="label">{t("labelText")}</Label>
            <Input
              id="label"
              value={localComponent.label}
              onChange={(e) => updateProperty("label", e.target.value)}
              placeholder={t("enterLabelText")}
            />
          </div>

          {hasPlaceholder && (
            <div className="space-y-2">
              <Label htmlFor="placeholder">{t("placeholder")}</Label>
              <Input
                id="placeholder"
                value={localComponent.placeholder || ""}
                onChange={(e) => updateProperty("placeholder", e.target.value)}
                placeholder={t("enterPlaceholderText")}
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <Label htmlFor="required">{t("required")}</Label>
            <Switch
              id="required"
              checked={localComponent.required || false}
              onCheckedChange={(checked) => updateProperty("required", checked)}
            />
          </div>
        </div>

        {/* 特殊属性 */}
        {hasHeadingLevel && (
          <>
            <Separator />
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">
                {t("headingSettings")}
              </h4>
              <div className="space-y-2">
                <Label htmlFor="heading-level">{t("headingLevel")}</Label>
                <Select
                  value={String(localComponent.properties?.level || 1)}
                  onValueChange={(value) =>
                    updateNestedProperty("properties", "level", Number(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">{t("h1MainTitle")}</SelectItem>
                    <SelectItem value="2">{t("h2Subtitle")}</SelectItem>
                    <SelectItem value="3">{t("h3ThirdLevel")}</SelectItem>
                    <SelectItem value="4">{t("h4FourthLevel")}</SelectItem>
                    <SelectItem value="5">{t("h5FifthLevel")}</SelectItem>
                    <SelectItem value="6">{t("h6SixthLevel")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}

        {hasButtonVariant && (
          <>
            <Separator />
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">
                {t("buttonSettings")}
              </h4>
              <div className="space-y-2">
                <Label htmlFor="button-variant">{t("buttonStyle")}</Label>
                <Select
                  value={localComponent.properties?.variant || "primary"}
                  onValueChange={(value) =>
                    updateNestedProperty("properties", "variant", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">
                      {t("primaryButton")}
                    </SelectItem>
                    <SelectItem value="secondary">
                      {t("secondaryButton")}
                    </SelectItem>
                    <SelectItem value="outline">
                      {t("outlineButton")}
                    </SelectItem>
                    <SelectItem value="ghost">{t("ghostButton")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}

        {/* 选项设置 */}
        {hasOptions && (
          <>
            <Separator />{" "}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">
                  {t("optionSettings")}
                </h4>
                <Button size="sm" variant="outline" onClick={addOption}>
                  <Plus className="w-4 h-4 mr-1" />
                  {t("addOption")}
                </Button>
              </div>

              <div className="space-y-3">
                {(localComponent.options || []).map((option, index) => (
                  <Card key={index} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm">
                          {t("option")} {index + 1}
                        </Label>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeOption(index)}
                          className="h-6 w-6 p-0 text-red-600"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                      <Input
                        placeholder={t("optionLabel")}
                        value={option.label}
                        onChange={(e) => {
                          let v = e.target.value;
                          v = v === "" ? " " : v;
                          updateOption(index, v);
                        }}
                      />
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </>
        )}

        <Separator />

        {/* 验证规则 */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">{t("validationRules")}</h4>

          {localComponent.type === "text-input" && (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-sm">{t("minLength")}</Label>
                  <Input type="number" placeholder="0" min="0" />
                </div>
                <div>
                  <Label className="text-sm">{t("maxLength")}</Label>
                  <Input type="number" placeholder={t("unlimited")} min="0" />
                </div>
              </div>
            </div>
          )}

          {localComponent.type === "email-input" && (
            <div className="flex items-center justify-between">
              <Label className="text-sm">{t("emailValidation")}</Label>
              <Switch defaultChecked />
            </div>
          )}

          {localComponent.type === "phone-input" && (
            <div className="flex items-center justify-between">
              <Label className="text-sm">{t("phoneValidation")}</Label>
              <Switch defaultChecked />
            </div>
          )}
        </div>

        <Separator />

        {/* 样式设置 */}
        {/* <div className="space-y-4">
          <h4 className="font-medium text-gray-900">{t("styleSettings")}</h4>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-sm">{t("width")}</Label>
              <Select defaultValue="100%">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="25%">25%</SelectItem>
                  <SelectItem value="50%">50%</SelectItem>
                  <SelectItem value="75%">75%</SelectItem>
                  <SelectItem value="100%">100%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-sm">{t("alignment")}</Label>
              <Select defaultValue="left">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">{t("leftAlign")}</SelectItem>
                  <SelectItem value="center">{t("centerAlign")}</SelectItem>
                  <SelectItem value="right">{t("rightAlign")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div> */}
      </CardContent>
    </div>
  );
};
