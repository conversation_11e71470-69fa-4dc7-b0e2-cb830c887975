import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {Loader2, Save} from "lucide-react";
import { useEffect, useState } from "react";
import { formApi } from "../../../services/api/form";
import { TemplateResponse } from "../types/form-types";
import { FormPagination } from "./FormPagination";

interface TemplateListProps {
  onPreviewTemplate: (template: TemplateResponse) => void;
  onCreateFromTemplate: (template: TemplateResponse) => void;
  selectedTemplateId?: string;
  disableScratch?: boolean; // 是否禁用 From Scratch 模板
}

const getTemplateIcon = (templateName: string) => {
  const name = templateName.toLowerCase();
  if (name.includes("feedback") || name.includes("反馈")) {
    return MessageSquare;
  } else if (name.includes("contact") || name.includes("联系")) {
    return Phone;
  } else if (name.includes("registration") || name.includes("注册")) {
    return UserPlus;
  } else if (name.includes("survey") || name.includes("调查")) {
    return BarChart3;
  }
  return FileText;
};

const getTemplateColor = (templateName: string) => {
  const name = templateName.toLowerCase();
  if (name.includes("feedback") || name.includes("反馈")) {
    return "bg-green-100 text-green-600";
  } else if (name.includes("contact") || name.includes("联系")) {
    return "bg-purple-100 text-purple-600";
  } else if (name.includes("registration") || name.includes("注册")) {
    return "bg-orange-100 text-orange-600";
  } else if (name.includes("survey") || name.includes("调查")) {
    return "bg-red-100 text-red-600";
  }
  return "bg-blue-100 text-blue-600";
};

import {
  BarChart3,
  FileText,
  MessageSquare,
  Phone,
  Plus,
  UserPlus,
} from "lucide-react";

const TemplateCard = ({
  template,
  onPreviewTemplate,
  onUseTemplate,
  isSelected,
}: {
  template: TemplateResponse;
  onPreviewTemplate: (template: TemplateResponse) => void;
  onUseTemplate: (template: TemplateResponse) => void;
  isSelected: boolean;
}) => {
  const Icon = getTemplateIcon(template.name);
  const colorClass = getTemplateColor(template.name);

  const handleCardClick = () => {
    onPreviewTemplate(template);
  };

  const handleUseTemplate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onUseTemplate(template);
  };

  return (
    <Card
      className={`cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 ${
        isSelected ? "border-2 border-blue-500" : ""
      }`}
      onClick={handleCardClick}
    >
      <CardContent className="p-6 flex flex-col h-full">
        <div className="flex justify-center mb-4">
          <div
              className={`w-12 h-12 ${colorClass} rounded-lg flex items-center justify-center`}
          >
            <Icon className="w-6 h-6" />
          </div>
        </div>

        <h3 className="font-semibold text-gray-900 mb-2 text-center">
          {template.name}
        </h3>
        <p className="text-sm text-gray-600 mb-4 line-clamp-2 text-center flex-grow">
          {template.description || "No description available"}
        </p>
        <div className="border-t border-gray-200 my-4"></div>
        <div className="flex justify-center mt-auto">
          <Button
              onClick={handleUseTemplate}
              className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
          >
            <Plus className="w-4 h-4 group-hover:scale-110 transition-transform" />
            Use Template
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export const TemplateList = ({
  onPreviewTemplate,
  onCreateFromTemplate,
  selectedTemplateId,
  disableScratch = false,
}: TemplateListProps) => {
  const { toast } = useToast();

  const [templates, setTemplates] = useState<TemplateResponse[]>([]);
  const [allTemplates, setAllTemplates] = useState<TemplateResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 分页状态
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  // 如果禁用了 Scratch 卡片，则每页显示4个模板，否则显示3个
  const pageSize = disableScratch ? 4 : 3;

  useEffect(() => {
    loadAllTemplates();
  }, []);

  useEffect(() => {
    updateDisplayedTemplates();
  }, [page, allTemplates]);

  const loadAllTemplates = async () => {
    try {
      setLoading(true);
      const result = await formApi.getFormTemplates();

      if (Array.isArray(result)) {
        setAllTemplates(result);
        setTotal(result.length);
      } else {
        setAllTemplates(result.items);
        setTotal(result.total);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load templates");
      toast({
        variant: "destructive",
        title: "Load failed",
        description: "Failed to load templates",
      });
    } finally {
      setLoading(false);
    }
  };
  const updateDisplayedTemplates = () => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setTemplates(allTemplates.slice(startIndex, endIndex));
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleCreateFromTemplate = (template: TemplateResponse) => {
    onCreateFromTemplate(template);
  };

  const handlePreviewTemplate = (template: TemplateResponse) => {
    onPreviewTemplate(template);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">{error}</p>
        <Button onClick={loadAllTemplates} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="mb-12">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Form Templates
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* From Scratch 卡片 - 固定显示在第一个位置 */}
          {!disableScratch && (
            <Card
              className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 border-dashed border-gray-300 hover:border-blue-400"
              onClick={() => onCreateFromTemplate({} as TemplateResponse)}
            >
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-6 h-6 text-gray-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  From Scratch
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Start with a blank canvas
                </p>
              </CardContent>
            </Card>
          )}

          {/* 动态渲染模板卡片 - 显示当前页的模板 */}
          {templates.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
              onPreviewTemplate={handlePreviewTemplate}
              onUseTemplate={handleCreateFromTemplate}
              isSelected={selectedTemplateId === template.id}
            />
          ))}
        </div>

        {/* 模板分页 - 只有超过pageSize个模板时才显示 */}
        {total > pageSize && (
          <FormPagination
            currentPage={page}
            totalItems={total}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            className="mt-6"
          />
        )}
      </div>
    </div>
  );
};
