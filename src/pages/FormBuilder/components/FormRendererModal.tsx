import {
  Dialog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FormConfig } from "../types/form-types";
import { FormRenderer } from "./FormRenderer";

interface FormRendererModalProps {
  config?: FormConfig;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const FormRendererModal = ({
  config,
  open,
  onOpenChange,
}: FormRendererModalProps) => {
  if (!config) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Form Preview</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <FormRenderer
            config={config}
            onSubmit={async (): Promise<void> => {}}
            className="shadow-none border-0"
          />
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
