import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { ArrowLeft, ChevronDown, Eye, Loader2, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { formApi } from "../../services/api/form";
import { ComponentLibrary } from "./components/ComponentLibrary";
import { DesignCanvas } from "./components/DesignCanvas";
import { PropertyPanel } from "./components/PropertyPanel";
import {
  FormComponent,
  FormConfig,
  TemplateResponse,
} from "./types/form-types";
import { serializeForm } from "./utils/form-serializer";

interface FormDesignerProps {
  showFormTemplate?: boolean;
  disablePreview?: boolean;
  formConfig?: FormConfig;
  saveForm?: (formConfig: FormConfig) => Promise<FormConfig>;
  backToList?: () => void;
}

export const FormDesigner = ({
  saveForm,
  backToList,
  formConfig,
  disablePreview,
  showFormTemplate,
}: FormDesignerProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [formId, setFormId] = useState<string>("");
  const [formTitle, setFormTitle] = useState("");
  const [formDescription, setFormDescription] = useState("");
  const [formLogo, setFormLogo] = useState<string | null>(null);
  const [selectedComponent, setSelectedComponent] =
    useState<FormComponent | null>(null);
  const [components, setComponents] = useState<FormComponent[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [templates, setTemplates] = useState<TemplateResponse[]>([]);

  // 初始化表单
  useEffect(() => {
    const editFormId = searchParams.get("formId");
    if (editFormId) {
      load(editFormId);
    }
  }, [searchParams]);

  useEffect(() => {
    if (formConfig) {
      loadForm(formConfig);
    }
  }, [formConfig]);

  useEffect(() => {
    if (showFormTemplate) {
      const fetchTemplates = async () => {
        try {
          const formTemplates = await formApi.getFormTemplates();
          if (Array.isArray(formTemplates)) {
            setTemplates(formTemplates);
          } else {
            setTemplates(formTemplates.items);
          }
        } catch (err) {
          console.error(err);
        }
      };
      fetchTemplates();
    }
  }, [showFormTemplate]);

  const handleCreateFromTemplate = async (template: TemplateResponse) => {
    try {
      let content;
      try {
        content = JSON.parse(template.content);
      } catch (e) {
        console.error("Failed to parse template content", e);
        return;
      }
      const templateConfig = {
        formId: "",
        title: template.name,
        description: template.description,
        logo: template.logo,
        ...content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await saveForm?.(templateConfig);
      toast({
        title: t("templateCreatedSuccessfully"),
        description: `${templateConfig.title}${t("templateCreated")}`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("templateCreationFailed"),
        description:
          error instanceof Error ? error.message : t("templateCreationFailed"),
      });
    }
  };

  const load = async (formIdToLoad: string) => {
    const config = await formApi.loadForm(formIdToLoad);
    loadForm(config);
  };

  // 加载表单
  const loadForm = async (config: FormConfig) => {
    console.log("Form config provided:", formConfig);
    setLoading(true);
    try {
      setFormId(config.formId);
      setFormTitle(config.title);
      setFormDescription(config.description || "");
      setFormLogo(config.logo || null);
      setComponents(config.components);
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("loadFailed"),
        description:
          error instanceof Error ? error.message : t("formLoadFailed"),
      });
    } finally {
      setLoading(false);
    }
  };

  const onSaveClick = async () => {
    setSaving(true);
    try {
      await handleSave();
      backToList && backToList();
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("saveFailed"),
        description:
          error instanceof Error ? error.message : t("formSaveFailed"),
      });
    } finally {
      setSaving(false);
    }
  };

  const handleSave = async (): Promise<FormConfig> => {
    const formConfig = serializeForm(
      formId,
      formTitle,
      formDescription,
      components,
      undefined, // 使用默认设置
      formLogo
    );

    // 如果提供了 saveForm 函数，则调用它，否则使用默认的保存逻辑
    if (saveForm) {
      const form = await saveForm(formConfig);
      toast({
        title: t("saveSuccess"),
        description: t("formSavedSuccessfully"),
      });
      return form;
    }
  };

  const handlePreview = () => {
    // 先保存再预览
    handleSave().then((f) => {
      navigate(`/form-preview/${f.formId}`);
    });
  };

  const handleAddComponent = (component: FormComponent) => {
    const newComponent = {
      ...component,
      id: `component_${Date.now()}`,
    };
    setComponents((prev) => [...prev, newComponent]);
  };

  const handleSelectComponent = (component: FormComponent | null) => {
    setSelectedComponent(component);
  };

  const handleUpdateComponent = (updatedComponent: FormComponent) => {
    setComponents((prev) =>
      prev.map((comp) =>
        comp.id === updatedComponent.id ? updatedComponent : comp
      )
    );
    setSelectedComponent(updatedComponent);
  };

  const handleDeleteComponent = (componentId: string) => {
    setComponents((prev) => prev.filter((comp) => comp.id !== componentId));
    if (selectedComponent?.id === componentId) {
      setSelectedComponent(null);
    }
  };

  const handleReorderComponents = (newComponents: FormComponent[]) => {
    setComponents(newComponents);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部工具栏 - 固定高度，不滚动 */}
      <div className="flex-shrink-0 flex justify-between border-b bg-white px-6 py-4">
        <div className="flex items-center justify-start space-x-2">
          {backToList && (
            <Button onClick={backToList} variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t("backToList")}
            </Button>
          )}
        </div>
        <div className="flex items-center justify-end space-x-2">
          {/* <Button variant="outline" size="sm">
            <Undo className="w-4 h-4 mr-1" />
            {t("undo")}
          </Button>
          <Button variant="outline" size="sm">
            <Redo className="w-4 h-4 mr-1" />
            {t("redo")}
          </Button>
          <Separator orientation="vertical" className="h-6" />*/}
          {showFormTemplate && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  {t("template")}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {templates.map((template) => (
                  <DropdownMenuItem
                    key={template.id}
                    onClick={() => handleCreateFromTemplate(template)}
                  >
                    {template.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          {!disablePreview && (
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={loading || saving}
            >
              <Eye className="w-4 h-4 mr-1" />
              {t("previewForm")}
            </Button>
          )}
          <Button size="sm" onClick={onSaveClick} disabled={loading || saving}>
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                {t("savingForm")}
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-1" />
                {t("saveForm")}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 主要内容区域 - 剩余高度，三个区域分别滚动 */}
      <div className="flex-1 flex min-h-0">
        {/* 左侧组件库 - 独立滚动 */}
        <div className="w-80 border-r bg-gray-50 flex-shrink-0 overflow-y-auto">
          <ComponentLibrary onAddComponent={handleAddComponent} />
        </div>

        {/* 中央设计画布 - 独立滚动 */}
        <div className="flex-1 bg-gray-100 overflow-y-auto">
          <DesignCanvas
            components={components}
            selectedComponent={selectedComponent}
            onSelectComponent={handleSelectComponent}
            onUpdateComponent={handleUpdateComponent}
            onDeleteComponent={handleDeleteComponent}
            onReorderComponents={handleReorderComponents}
            formTitle={formTitle}
            formDescription={formDescription}
            formLogo={formLogo}
            onUpdateFormTitle={setFormTitle}
            onUpdateFormDescription={setFormDescription}
            onUpdateFormLogo={setFormLogo}
            loading={loading}
            saving={saving}
          />
        </div>

        {/* 右侧属性面板 - 独立滚动 */}
        <div className="w-80 border-l bg-white flex-shrink-0 overflow-y-auto">
          <PropertyPanel
            selectedComponent={selectedComponent}
            onUpdateComponent={handleUpdateComponent}
          />
        </div>
      </div>
    </div>
  );
};