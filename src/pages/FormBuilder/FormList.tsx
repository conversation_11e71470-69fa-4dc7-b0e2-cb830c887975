import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Calendar,
  ChevronDown,
  Copy,
  Edit,
  Eye,
  FileText,
  Loader2,
  Plus,
  Search,
  Trash2,
  ListOrdered,
  CheckCircle,
  Circle,
  MoreVertical, Link,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { formApi } from "../../services/api/form";
import { FormConfig, TemplateResponse, Page } from "./types/form-types";
import { FormSubmissionsModal } from "./components/FormSubmissionsModal";
import { FormPagination } from "./components/FormPagination";
import { TemplateList } from "./components/TemplateList";

export const FormList = ({ projectId, toDesigner }: { projectId: string; toDesigner: () => void; }) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [forms, setForms] = useState<FormConfig[]>([]);
  const [allForms, setAllForms] = useState<FormConfig[]>([]); // 存储所有表单数据，用于搜索
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const [showSubmissionsModal, setShowSubmissionsModal] = useState(false);

  // 表单分页状态
  const [formPage, setFormPage] = useState(1);
  const [formTotal, setFormTotal] = useState(0);
  const formPageSize = 6; // 每页显示6个表单

  useEffect(() => {
    if (projectId) {
      loadAllForms();
    }
  }, [projectId]);

  useEffect(() => {
    updateDisplayedForms();
  }, [formPage, allForms, searchQuery]);

  const loadAllForms = async () => {
    setLoading(true);
    setError(null);

    try {
      // 加载所有表单数据（不分页）
      const result = await formApi.getProjectForms(projectId);

      if (Array.isArray(result)) {
        setAllForms(result);
        setFormTotal(result.length);
      } else {
        setAllForms(result.items);
        setFormTotal(result.total);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载表单列表失败");
    } finally {
      setLoading(false);
    }
  };

  const updateDisplayedForms = () => {
    let formsToDisplay = allForms;

    // 如果有搜索查询，先过滤
    if (searchQuery) {
      formsToDisplay = allForms.filter(
        (form) =>
          form.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          form.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // 然后分页
    const startIndex = (formPage - 1) * formPageSize;
    const endIndex = startIndex + formPageSize;
    setForms(formsToDisplay.slice(startIndex, endIndex));
  };

  const handleCreateForm = () => {
    toDesigner();
  };

  const handlePreviewTemplate = (template: TemplateResponse) => {
    navigate(`/form-preview/template-${template.id}`);
  };

  const handleCreateFromTemplate = async (template: TemplateResponse) => {
    // 如果是空模板（From Scratch），直接跳转到设计器
    if (!template.id) {
      handleCreateForm();
      return;
    }

    try {
      let content;
      try {
        content = JSON.parse(template.content);
      } catch (e) {
        console.error("Failed to parse template content", e);
        return;
      }
      const templateConfig = {
        formId: "",
        title: template.name,
        description: template.description,
        logo: template.logo,
        ...content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await formApi.saveForm(projectId, templateConfig);
      toast({
        title: t("templateCreatedSuccessfully"),
        description: `${templateConfig.title}${t("templateCreated")}`,
      });
      loadAllForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("templateCreationFailed"),
        description:
          error instanceof Error ? error.message : t("templateCreationFailed"),
      });
    }
  };

  const handleEditForm = (formId: string) => {
    navigate(`/form-builder?formId=${formId}`);
  };

  const handlePreviewForm = (formId: string) => {
    navigate(`/form-preview/${formId}`);
  };

  const handleDuplicateForm = async (form: FormConfig) => {
    try {
      await formApi.duplicateForm(form.formId);
      toast({
        title: t("copySuccess"),
        description: t("formCopiedSuccessfully"),
      });
      loadAllForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("copyFailed"),
        description:
          error instanceof Error ? error.message : t("formCopyFailed"),
      });
    }
  };

  const handleToggleFormActive = async (formId: string, currentActiveState: boolean) => {
    if (currentActiveState) return;

    try {
      await formApi.setActive(formId);
      /*toast({
        title: t("active"),
        description: t("formActivated"),
      });*/
      loadAllForms(); // 重新加载列表以显示更新后的状态
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("saveFailed"),
        description:
          error instanceof Error ? error.message : t("saveFailed"),
      });
    }
  };

  const handleDeleteForm = async (formId: string) => {
    try {
      await formApi.deleteForm(formId);
      toast({
        title: t("deleteSuccess"),
        description: t("formDeletedSuccessfully"),
      });
      loadAllForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("deleteFailed"),
        description:
          error instanceof Error ? error.message : t("formDeleteFailed"),
      });
    }
  };

  const handleCopyShareUrl = (formId: string) => {
    const shareUrl = `${window.location.origin}/form/${formId}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: t("linkCopied"),
      description: t("shareLinkCopied"),
    });
  };

  const handleFormPageChange = (page: number) => {
    setFormPage(page);
  };

  // 搜索时重置分页
  useEffect(() => {
    if (searchQuery) {
      setFormPage(1);
    }
  }, [searchQuery]);

  // 获取过滤后的表单总数（用于显示和分页计算）
  const getFilteredFormsCount = () => {
    if (searchQuery) {
      return allForms.filter(
        (form) =>
          form.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          form.description?.toLowerCase().includes(searchQuery.toLowerCase())
      ).length;
    }
    return allForms.length;
  };

  // 格式化时间为年/月/日 时:分:秒
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 头部区域 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t("formManagement")}
            </h1>
            <p className="text-gray-600 mt-2">{t("createAndManageForms")}</p>
          </div>

          <div className="flex items-center space-x-3">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={t("searchForms")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
        </div>

        {/* 模板区域 */}
        <TemplateList
          onPreviewTemplate={handlePreviewTemplate}
          onCreateFromTemplate={handleCreateFromTemplate}
        />

        {/* My Forms 区域 */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">My Forms</h2>
          </div>

          {forms.length === 0 ? (
            <Card className="border-dashed border-2">
              <CardContent className="py-16 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchQuery ? t("noFormsFound") : "No forms yet"}
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery ? t("tryDifferentSearch") : "Create your first form to get started"}
                </p>
                {!searchQuery && (
                  <Button onClick={handleCreateForm} className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out">
                    <Plus className="w-4 h-4 mr-2" />
                    {t("newForm")}
                  </Button>

                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {forms.map((form) => (
                <Card
                  key={form.formId}
                  className="hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
                >
                <CardContent className="p-6">
                  {/* 顶部：状态按钮和更多操作菜单 */}
                  <div className="flex items-center justify-between mb-4">
                            {/* 左侧：可点击的状态按钮 */}
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleToggleFormActive(form.formId, !!form.active)}
                                className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                      form.active
                        ? 'bg-green-100 text-green-800 hover:bg-green-200 border-green-300'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-300'
                                }`}
                            >
                      {form.active ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </>
                      ) : (
                        <>
                          <Circle className="w-3 h-3 mr-1" />
                          Inactive
                        </>
                      )}
                    </Button>

                            {/* 右侧：更多操作下拉菜单 */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-8 w-8 p-0"
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                    onClick={() => handleDuplicateForm(form)}
                                    className="flex items-center"
                                >
                                  <Copy className="w-4 h-4 mr-2" />
                                  {t("copyForm")}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => handleCopyShareUrl(form.formId)}
                                    className="flex items-center"
                                >
                                  <Link className="w-4 h-4 mr-2" />
                                  {t("copyLink")}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => handleDeleteForm(form.formId)}
                                    className="flex items-center text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  {t("deleteForm")}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                  </div>

                  {/* 表单标题和描述 */}
                  <div className="mb-4">
                    <h3 className="font-semibold text-gray-900 text-lg mb-2 line-clamp-1">
                      {form.title}
                    </h3>
                      {form.description || 'No description provided'}
                    <p className="text-sm text-gray-600 line-clamp-2 min-h-[2.5rem]">
                    </p>
                  </div>

                  {/* 表单统计 */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <FileText className="w-4 h-4" />
                      <span>
                        {form.components.length} {t("components")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {formatDateTime(form.updatedAt)}
                      </span>
                    </div>
                  </div>

                  {/* 主要操作按钮 */}
                  <div className="flex items-center justify-center space-x-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditForm(form.formId)}
                            className="flex-1"
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            {t("edit")}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("editForm")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePreviewForm(form.formId)}
                            className="flex-1"
                          >
                            <Eye className="w-4 h-4 mr-2" />
                        {t("preview")}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("previewForm")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedFormId(form.formId);
                              setShowSubmissionsModal(true);
                            }}
                            className="flex-1"
                          >
                            <ListOrdered className="w-4 h-4 mr-2" />
                                  {t("submissions")}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("viewSubmissions")}</p>
                        </TooltipContent>
                      </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          )}

          {/* 表单分页 */}
          {getFilteredFormsCount() > formPageSize && (
            <FormPagination
              currentPage={formPage}
              totalItems={getFilteredFormsCount()}
              pageSize={formPageSize}
              onPageChange={handleFormPageChange}
              className="mt-6"
            />
          )}
        </div>
      </div>

      <FormSubmissionsModal
        formId={selectedFormId || ""}
        open={showSubmissionsModal}
        onOpenChange={setShowSubmissionsModal}
      />
    </div>
  );
};