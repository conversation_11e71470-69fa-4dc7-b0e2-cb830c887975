import {useState, useEffect, useRef} from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { OnboardingProgress } from '@/components/OnboardingProgress';
import { useOnboardingData } from '@/hooks/useOnboardingData';
import {useAuth} from "@/context/AuthContext.tsx";
import { useToast } from '@/hooks/use-toast';
import { toast } from '@/hooks/use-toast';

const OnboardingStarter = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { data, updateData } = useOnboardingData();
  const [websiteUrl, setWebsiteUrl] = useState(data.websiteUrl);
  const [urlError, setUrlError] = useState('');
  const { toast } = useToast();
  const hasInitialized = useRef(false);

  // URL验证函数
  const validateUrl = (url) => {
    if (!url || !url.trim()) {
      return { isValid: false, error: '' }; // 空值不显示错误，但不是有效的
    }

    const trimmedUrl = url.trim();

    // 检查是否以http://或https://开头
    if (!/^https?:\/\//i.test(trimmedUrl)) {
      return {
        isValid: false,
        error: 'URL must start with http:// or https://'
      };
    }

    try {
      const urlObj = new URL(trimmedUrl);

      // 检查协议是否为http或https
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          isValid: false,
          error: 'URL must use HTTP or HTTPS protocol'
        };
      }

      // 检查是否有有效的域名
      if (!urlObj.hostname || urlObj.hostname.length < 1) {
        return {
          isValid: false,
          error: 'Please enter a valid domain name'
        };
      }

      // 检查域名格式（基本检查）
      const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
      if (!domainRegex.test(urlObj.hostname)) {
        return {
          isValid: false,
          error: 'Please enter a valid domain name'
        };
      }

      // 检查顶级域名（至少有一个点）
      if (!urlObj.hostname.includes('.')) {
        return {
          isValid: false,
          error: 'Please enter a complete domain (e.g., example.com)'
        };
      }

      return {
        isValid: true,
        error: '',
        normalizedUrl: trimmedUrl
      };
    } catch (error) {
      return {
        isValid: false,
        error: 'Please enter a valid website URL'
      };
    }
  };

  // 检查URL是否有效（非空且已修剪空白字符且格式正确）
  const validation = validateUrl(websiteUrl);
  const isUrlValid = validation.isValid;

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 分离初始化逻辑
  useEffect(() => {
    if (!hasInitialized.current && data.websiteUrl) {
      setWebsiteUrl(data.websiteUrl);
      hasInitialized.current = true;
    }
  }, [data.websiteUrl]);

  // 实时验证URL
  useEffect(() => {
    if (websiteUrl && websiteUrl.trim()) {
      const validation = validateUrl(websiteUrl);
      setUrlError(validation.error);
    } else {
      setUrlError('');
    }
  }, [websiteUrl]);

  const handleNext = () => {
    if (!websiteUrl || !websiteUrl.trim()) {
      toast({
        title: "Website URL is required",
        description: "Please enter your website address to continue.",
        variant: "destructive",
      });
      return;
    }

    const validation = validateUrl(websiteUrl);

    if (!validation.isValid) {
      toast({
        title: "Invalid URL format",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    updateData({ websiteUrl: validation.normalizedUrl });

    // 可选：添加小延迟确保数据已保存
    setTimeout(() => {
      navigate('/onboarding-form');
    }, 100);
  };

  return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
        <OnboardingProgress currentStep={1} />

        {/* Main content area */}
        <div className="flex-1 flex flex-col relative">
          {/* Content positioned higher up */}
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="w-full max-w-4xl mx-auto">
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  What's your website address?
                </h1>
                <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
                  Provide your website URL to help train your AI agent. We'll start by pulling in
                  key data to get your chatbot ready in minutes.
                </p>

                <div className="max-w-md mx-auto mb-8">
                  <div className="space-y-2">
                    <Input
                        type="url"
                        placeholder="https://example.com"
                        value={websiteUrl || ''}
                        onChange={(e) => setWebsiteUrl(e.target.value)}
                        className={`h-12 text-base ${
                            urlError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                    />

                    {/* Error message */}
                    {urlError && (
                        <div className="text-sm text-red-600 text-left mt-1 flex items-center">
                          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {urlError}
                        </div>
                    )}

                    {/* Success indicator */}
                    {websiteUrl && websiteUrl.trim() && isUrlValid && !urlError && (
                        <div className="text-sm text-green-600 text-left mt-1 flex items-center">
                          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          Valid website URL
                        </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fixed bottom right button */}
          <div className="absolute bottom-8 right-8">
            <Button
                onClick={handleNext}
                disabled={!isUrlValid}
                className={`px-8 py-2 h-10 transition-all duration-200 ${
                    !isUrlValid
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed hover:bg-gray-300'
                        : 'bg-primary text-primary-foreground hover:bg-primary/90'
                }`}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
  );
};

export default OnboardingStarter;