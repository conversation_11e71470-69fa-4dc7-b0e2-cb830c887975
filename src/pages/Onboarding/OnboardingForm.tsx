import { OnboardingProgress } from "@/components/OnboardingProgress";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext.tsx";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { TemplateList } from "@/pages/FormBuilder/components/TemplateList";
import {
  FormConfig,
  TemplateResponse,
} from "@/pages/FormBuilder/types/form-types";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FormRendererModal } from "../FormBuilder/components/FormRendererModal";

const OnboardingForm = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { formData, updateFormData } = useOnboardingData();
  const [selectedTemplateId, setSelectedTemplateId] = useState<
    string | undefined
  >(undefined);
  const [previewTemplate, setPreviewTemplate] = useState<
    TemplateResponse | undefined
  >(undefined);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading]);

  useEffect(() => {
    if (formData && formData.formId) {
      setSelectedTemplateId(formData.formId);
    }
  }, [formData]);

  const handleNext = () => {
    navigate("/onboarding-chatbot");
  };

  const handleBack = () => {
    navigate("/onboarding-starter");
  };

  const toFormConfig = (template: TemplateResponse): FormConfig => {
    let content;
      try {
        content = JSON.parse(template.content);
        content["formId"] = template.id;
      } catch (e) {
        console.error("Failed to parse template content", e);
        return;
      }

      const templateConfig = {
        formId: template.id,
        title: template.name,
        description: template.description,
        logo: template.logo,
        ...content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    return templateConfig;
  };

  const handleCreateFromTemplate = async (template: TemplateResponse) => {
    try {
      const templateConfig = toFormConfig(template);
      saveForm(templateConfig);
    } catch (error) {
      console.error("Failed to create form from template", error);
    }
  };

  const handlePreviewTemplate = (template: TemplateResponse) => {
    // 在实际应用中，这里可以导航到预览页面
    setPreviewTemplate(template);
  };

  const saveForm = async (formConfig: FormConfig): Promise<FormConfig> => {
    updateFormData(formConfig);
    return formConfig;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col relative">
      <OnboardingProgress currentStep={2} />

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="w-full max-w-6xl mx-auto">
          {/* Centered header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Form Designer
            </h1>
            <p className="text-gray-600">
              Design your lead capture form for the chatbot
            </p>
          </div>
          <TemplateList
            onPreviewTemplate={handlePreviewTemplate}
            onCreateFromTemplate={handleCreateFromTemplate}
            selectedTemplateId={selectedTemplateId}
            disableScratch={true} // 禁用 From Scratch 模板
          />
          <FormRendererModal
            open={!!previewTemplate}
            config={toFormConfig(previewTemplate)}
            onOpenChange={() => setPreviewTemplate(undefined)}
          />
        </div>
      </div>

      {/* Fixed navigation buttons in bottom right */}
      <div className="fixed bottom-8 right-8 flex items-center gap-4">
        <Button
          variant="outline"
          onClick={handleBack}
          className="px-8 py-2 h-10"
        >
          Back
        </Button>
        <Button onClick={handleNext} className="px-8 py-2 h-10">
          Next
        </Button>
      </div>
    </div>
  );
};

export default OnboardingForm;
