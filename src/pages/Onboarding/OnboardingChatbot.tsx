import ChatbotConfigForm from "@/components/ChatbotConfigForm";
import { ChatPreview } from "@/components/ChatPreview";
import { OnboardingProgress } from "@/components/OnboardingProgress";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useProject } from "@/context/ProjectContext";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { useTranslation } from "@/hooks/useTranslation";
import { getWebsitMetadata } from "@/services/api/projects";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChatbotConfig } from "@/components/ChatbotConfigForm"; // 导入ChatbotConfig类型

const OnboardingChatbot = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const { data, formData, updateData, createProjectAndForm } =
    useOnboardingData();
  const [isCreating, setIsCreating] = useState(false);
  const { refreshProjects, setCurrentProject } = useProject();

  // 使用单个ChatbotConfig对象存储所有配置
  const [chatbotConfig, setChatbotConfig] = useState<ChatbotConfig>({
    brandName: "YapYapBot",
    brandColor: "#9067bc",
    logo: "",
    welcomeMessage: t("defaultWelcomeMessage"),
    suggestedQuestionsEnabled: true,
    suggestedQuestions: [
      t("defaultSuggestedQuestion1"),
      t("defaultSuggestedQuestion2"),
      t("defaultSuggestedQuestion3"),
    ],
    formEnable: false,
    externalFormUrl: "",
    internalFormUrl: "",
    currentForm: 0,
    whatsappEnable: false,
    whatsappAddress: "",
    notificationEnabled: false,
    notificationEmails: [],
    customPrompt: "",
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading]);

  useEffect(() => {
    updateData({
      ...chatbotConfig,
    });
  }, [chatbotConfig]);

  useEffect(() => {
    if (formData && formData.title) {
      setChatbotConfig({
        ...chatbotConfig,
        formEnable: true,
        currentForm: 0,
      });
    }
    if (data.websiteUrl) {
      fetchWebsitMetadata(data.websiteUrl);
    }
  }, []);

  const fetchWebsitMetadata = async (url: string) => {
    const res = await getWebsitMetadata(url);
    if (res) {
      setChatbotConfig((prev) => {
        const updatedConfig = { ...prev };
        if (res.logo) {
          updatedConfig.logo = res.logo;
        }
        if (res.title) {
          updatedConfig.brandName =
            res.title.length > 20 ? res.title.substring(0, 20) : res.title;
        }
        return updatedConfig;
      });
    }
  };

  const handleStartNow = async () => {
    setIsCreating(true);

    try {
      const response = await createProjectAndForm();
      toast({
        title: t("createProjectSuccess"),
        description: `${t("project")} "${response.name}" ${t("createSuccess")}`,
      });

      // 刷新项目列表并设置新创建的项目为当前项目
      await refreshProjects();
      setCurrentProject(response);

      navigate("/chats");
    } catch (error) {
      console.error("Failed to create project:", error);
      toast({
        variant: "destructive",
        title: t("createProjectFailed"),
        description: error instanceof Error ? error.message : t("createFailed"),
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleBack = () => {
    navigate("/onboarding-form");
  };

  // 创建更新配置的函数
  const updateConfig: <K extends keyof ChatbotConfig>(
    field: K,
    value: ChatbotConfig[K]
  ) => void = (field, value) => {
    setChatbotConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col relative">
      <OnboardingProgress currentStep={3} />

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-4 pb-20">
        <div className="w-full max-w-6xl mx-auto">
          {/* 居中的标题和描述 */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Customize your AI Agent
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We tried to match your website with your colors, logos, and brand
              personality. Customize it below:
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start justify-items-center">
            {/* Left side - Chat预览，固定位置 */}
            <div className="flex items-start justify-center lg:justify-start lg:sticky lg:top-4">
              <div style={{ height: "600px", width: "350px" }}>
                <ChatPreview
                  assistantName={chatbotConfig.brandName}
                  welcomeMessage={chatbotConfig.welcomeMessage}
                  color={chatbotConfig.brandColor}
                  suggestedQuestions={
                    chatbotConfig.suggestedQuestionsEnabled
                      ? chatbotConfig.suggestedQuestions
                      : []
                  }
                  showInput={false}
                  compact={false}
                  className="h-full w-full"
                  logo={chatbotConfig.logo}
                  notCreateSession={true}
                  trackUrls={[
                    chatbotConfig.internalFormUrl || "",
                    chatbotConfig.externalFormUrl || "",
                    chatbotConfig.whatsappAddress || "",
                  ]}
                />
              </div>
            </div>

            {/* Right side - Form */}
            <div className="w-full">
              <ChatbotConfigForm
                config={chatbotConfig}
                updateConfig={updateConfig}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 固定在右下角的导航按钮 */}
      <div className="fixed bottom-6 right-6 flex items-center space-x-4 z-10">
        <Button
          variant="outline"
          onClick={handleBack}
          className="px-8 py-2 h-10"
        >
          Back
        </Button>
        {/* <Button onClick={handleNext} className="px-8 py-2 h-10">
            Next
          </Button> */}
        <Button
          onClick={handleStartNow}
          disabled={isCreating}
          className="px-8 py-2 h-10"
        >
          {isCreating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Creating...
            </>
          ) : (
            "Start Now"
          )}
        </Button>
      </div>
    </div>
  );
};

export default OnboardingChatbot;
