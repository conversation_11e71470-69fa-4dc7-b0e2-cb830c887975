// 配置文件
interface ApiConfig {
  baseUrl: string;
  projectsEndpoint: string;
  sessionEndpoint: string;
  chatEndpoint: string;
}

interface WidgetConfig {
  defaultColor: string;
  defaultAssistantName: string;
  defaultWelcomeMessage: string;
  defaultSuggestedQuestions: string[];
}

// Widget 默认配置
const WIDGET_DEFAULTS: WidgetConfig = {
  defaultColor: "#9067bc",
  defaultAssistantName: "YapYapBot",
  defaultWelcomeMessage: "Welcome! 👋\n\nI'm YapYapBot, here to assist with any questions you have. How can I help you today?",
  defaultSuggestedQuestions: [
    "I want to learn about product information.",
    "How to contact customer service?",
    "Are there any promotional activities?",
  ],
};

// 创建API配置的辅助函数
const createApiConfig = (baseUrl: string): ApiConfig => ({
  baseUrl,
  projectsEndpoint: `${baseUrl}/v1/projects`,
  sessionEndpoint: `${baseUrl}/v1/sessions`,
  chatEndpoint: `${baseUrl}/v1/chat`,
});

// 根据环境变量或构建模式确定配置
const isDevelopment = import.meta.env.DEV;
// Widget专用配置（用于编译后的widget.js）
export const getWidgetConfig = () => {
  // 直接使用固定的API地址，简化配置
  /*const baseUrl = isDevelopment
    ? "http://127.0.0.1:8081"
    : "https://www.yapyapbot.com";*/
  const baseUrl = "https://www.yapyapbot.com";
  return {
    api: createApiConfig(baseUrl),
    widget: WIDGET_DEFAULTS,
  };
};

export const supportMail = "<EMAIL>";

// 主应用聊天API配置
export const getChatApiUrl = () => {
  const protocol = window.location.protocol;
  const host = window.location.host;
  return `${protocol}//${host}/v1/chat`;
};

// 会话API配置
export const getSessionApiUrl = () => {
  const protocol = window.location.protocol;
  const host = window.location.host;
  return `${protocol}//${host}/v1/sessions`;
};
