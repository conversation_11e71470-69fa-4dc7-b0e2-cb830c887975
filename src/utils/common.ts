export function formatToUUID(sessionId: string) {
    if (sessionId.length !== 32) {
        throw new Error("Session ID must be 32 characters long");
    }

    // 按照UUID格式插入连字符：8-4-4-4-12
    return sessionId.replace(
        /^(.{8})(.{4})(.{4})(.{4})(.{12})$/,
        "$1-$2-$3-$4-$5"
    );
}

/**
 * 截断文本并在超出指定长度时添加省略号
 * @param text 要截断的文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number): string {
    if (!text || text.length <= maxLength) {
        return text;
    }
    
    return text.substring(0, maxLength) + "...";
}