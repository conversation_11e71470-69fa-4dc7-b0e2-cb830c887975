export default {
  // Navigation
  dashboard: "仪表板",
  projects: "项目管理",
  chats: "聊天记录",
  settings: "设置",
  channels: "渠道",
  chatbot: "聊天机器人",
  whatsapp: "WhatsAPP",
  voice: "语音",
  mail: "邮件",
  workflow: "工作流",
  form: "表单",
  pipeline: "流水线",
  general: "项目配置",
  testAndInstall: "测试与安装",
  sharePage: "体验地址",
  chinese: "中文",
  english: "EN",
  created: "创建时间",
  downloadTemplate: "下载模板",
  templateDownloaded: "模板下载成功",
  downloadFailed: "模板下载失败",
  downloading: "下载中...",

  // Auth
  login: "登录",
  loginSuccessful: "登录成功",
  loginFailed: "登录失败",
  loginFailedDescription: "用户名或密码不正确,或网络错误",
  logout: "退出登录",
  logoutDescription: "您已成功退出登录",
  register: "注册",
  email: "邮箱",
  password: "密码",
  confirmPassword: "确认密码",
  loginTitle: "欢迎回来",
  loginSubtitle: "登录您的AI客服平台",
  registerTitle: "创建账户",
  registerSubtitle: "开始构建您的AI客服",
  loginButton: "登录",
  registerButton: "创建账户",
  continueWithGoogle: "使用Google继续",
  orContinueWith: "或者使用以下方式继续",
  passwordTooWeak: "密码不符合安全要求",
  passwordMismatch: "密码不匹配",
  deleteAccount: "删除账户",
  deleteAccountConfirm: "请输入您的邮箱地址以确认删除",
  deleteAccountWarning:
    "此操作无法撤销。这将永久删除您的账户并从我们的服务器中删除所有数据。",
  accountDeleted: "账户删除成功",
  deleteAccountFailed: "删除账户失败",
  enterPassword: "输入您的密码",
  passwordRequired: "密码是必需的",
  deleting: "删除中...",

  // Form Builder
  selectFile: "选择文件",
  dragAndDropSupport: "拖拽文件到此处或点击选择",
  lastName: "姓",
  firstName: "名",
  phoneNumber: "电话号码",
  emailAddressLabel: "电子邮箱",
  enterPhone: "输入电话号码",
  enterEmail: "输入电子邮箱",
  bankCardNumber: "银行卡号",
  bankName: "银行名称",
  enterBankCardNumber: "输入银行卡号",
  enterBankName: "输入银行名称",
  streetAddress: "街道地址",
  enterStreetAddress: "输入街道地址",
  city: "城市",
  enterCity: "输入城市",
  stateProvince: "省/州",
  enterStateProvince: "输入省或州",
  country: "国家",
  enterCountry: "输入国家",
  postalCode: "邮政编码",
  enterPostalCode: "输入邮政编码",

  // Dashboard
  welcomeBack: "欢迎回来",
  totalVisitors: "总访客数",
  activeProjects: "活跃项目",
  totalChats: "总对话数",
  satisfactionRate: "满意度",
  recentActivity: "最近活动",

  // Project
  createProjectSuccess: "创建项目成功",
  createProjectFailed: "创建项目失败",
  myProjects: "我的项目",
  createProject: "创建新项目",
  projectName: "项目名称",
  websiteUrl: "网站地址",
  description: "描述",
  status: "状态",
  active: "活跃",
  inactive: "未激活",
  project: "项目",
  createSuccess: "已成功创建",
  createFailed: "创建项目时发生错误，请重试",
  saveConfig: "项目配置已保存",
  saveFailed: "保存失败，请重试",
  projectNotFound: "没有找到项目",
  returnToProjectList: "返回项目列表",

  // Knowledge Base
  knowledgeBase: "知识库",
  addDocument: "添加文档",
  uploadFile: "上传文件",
  addUrl: "添加网址",
  addFaq: "添加FAQ",
  addWebsiteUrl: "添加网站URL",
  uploadDocument: "上传文档",
  totalKnowledge: "知识库数量",
  totalWebsites: "网站页面",
  totalWords: "总字数",
  externalLinks: "外部链接",
  noMatchingContent: "没有找到匹配的内容",
  knowledgeBaseEmpty: "知识库为空",
  tryDifferentKeywords: "尝试使用不同的关键词搜索",
  addContentToStart: "添加网站URL或上传文档来开始构建您的知识库",
  addKnowledgeSuccess: "添加知识库成功",
  addKnowledgeFailed: "添加知识库失败，请稍后重试",
  addWebsiteDescription: "输入您想要添加到知识库的网站URL",
  supportedFileTypes: "支持的文件类型：PDF、DOC、DOCX、TXT、MD",
  clickToSelectFile: "点击选择文件或拖拽文件到此处",
  filesSelected: "已选择 {{count}} 个文件",

  // Widget Settings
  widgetSettings: "窗口设置",
  assistantName: "助手名称",
  welcomeMessage: "欢迎语",
  widgetColor: "窗口颜色",
  position: "位置",
  generateCode: "生成代码",

  // Pricing
  pricing: "价格方案",
  starterPlan: "入门版",
  growthPlan: "成长版",
  proPlan: "专业版",
  starterPrice: "¥199/月",
  growthPrice: "¥699/月",
  proPrice: "¥1999/月",
  starterFeatures: "每月最多1,000次对话，基础分析，邮件支持",
  growthFeatures: "每月最多10,000次对话，高级分析，优先支持，自定义品牌",
  proFeatures: "无限对话，企业级分析，24/7电话支持，API访问，自定义集成",
  choosePlan: "选择方案",

  // Project Creation Steps
  stepWebsite: "网站信息",
  stepKnowledge: "知识库",
  stepWidget: "窗口设置",
  stepCode: "嵌入代码",
  websiteUrlPlaceholder: "https://your-website.com",
  addFaqItem: "添加FAQ",
  question: "问题",
  answer: "答案",
  widgetPreview: "窗口预览",
  embedCode: "嵌入代码",
  copyCode: "复制代码",

  // Common
  save: "保存",
  cancel: "取消",
  edit: "编辑",
  delete: "删除",
  view: "查看",
  create: "创建",
  loading: "加载中...",
  success: "成功",
  error: "错误",
  next: "下一步",
  previous: "上一步",
  finish: "完成",
  back: "返回",

  // Brand
  appName: "AI客服平台",
  aiAssistant: "AI助手",
  newProject: "新建项目",

  // Footer
  companyDescription:
    "通过智能AI客服解决方案为您的业务赋能。为您的客户提供24/7卓越的支持体验。",
  supportEmail: "<EMAIL>",
  products: "产品",
  support: "支持",
  documentation: "文档",
  helpCenter: "帮助中心",
  contactUs: "联系我们",
  systemStatus: "状态",
  allRightsReserved: "版权所有。",
  privacyPolicy: "隐私政策",
  termsOfService: "服务条款",

  // Test & Install
  testInstall: "测试与安装",
  installChatbot: "在您的网站上安装聊天机器人",
  testChatbot: "测试您的聊天机器人",
  testChatbotDescription: "确保其完美运行",
  embedCodeDescription:
    "当您满意时，复制下面的代码并将其粘贴到您网站的 </body> 标签之前以激活它。",
  webChatEnabled: "网页聊天已启用",

  // Knowledge Base
  addWebsite: "添加网站",
  selectFiles: "选择文件",
  upload: "上传",
  uploading: "上传中...",
  documentUploaded: "文档上传成功",
  uploadFailed: "上传失败，请重试",
  manageKnowledgeBase: "管理您的AI助手的知识库内容",
  totalDocuments: "总文档数",

  // Chat Records
  conversations: "聊天记录",
  totalConversations: "会话总数",
  totalCountry: "总国家数",
  totalCity: "总城市数",
  allConversations: "所有对话",
  viewChatRecords: "查看和管理所有客服对话记录",
  noConversation: "暂无会话记录",
  selectConversation: "请选择一个会话",
  fetchConversationFailed: "获取会话失败",
  fetchChatsFailed: "获取聊天记录失败",
  summary: "摘要",
  detail: "详情",
  summaryContent: "摘要内容将在此处显示",

  // Analysis
  dataAnalysis: "数据分析",
  performanceAnalysis: "客服系统性能分析和洞察",
  conversationTrend: "对话趋势",
  satisfactionAnalysis: "满意度分析",
  activeHours: "活跃时段分析",
  exportReport: "导出报告",

  // Dashboard specific
  dashboardOverview: "这是您的聊天机器人性能概览",
  emailPlaceholder: "<EMAIL>",
  passwordPlaceholder: "••••••••",

  // Default project values
  defaultWelcomeMessage:
    "欢迎！👋\n我是YapYapBot，在这里协助您解答任何问题。今天我能为您做些什么？",
  defaultSuggestedQuestion1: "没有问题",
  defaultSuggestedQuestion2: "你好吗？",
  defaultSuggestedQuestion3: "我还有其他问题",

  // Hero section
  smartConversations: "智能对话",
  smartConversationsDesc: "基于AI的聊天，具备自然语言处理能力",
  quickSetup: "快速设置",
  quickSetupDesc: "通过简单的嵌入代码，几分钟内即可部署",
  secureReliable: "安全可靠",
  secureReliableDesc: "企业级安全保障，99.9%正常运行时间",
  analyticsDashboard: "分析仪表板",
  analyticsDashboardDesc: "全面的洞察和性能指标",

  // Chat Core messages
  unknown: "未知",
  sessionApiNotConfigured: "会话API URL未配置",
  sessionCreationFailed: "创建会话失败",
  chatErrorMessage: "抱歉，发生了错误。请稍后重试。",
  //demoResponseMessage: "感谢您的问题！这是一个演示回复。要启用真实对话，请确保项目ID已正确配置。",

  // Account page - new keys only
  accountPreferences: "您的账户偏好设置",
  personal: "个人信息",
  security: "安全设置",
  personalInformation: "个人信息",
  updatePersonalDetails: "更新您的个人详细信息和头像",
  name: "姓名",
  enterYourName: "输入您的姓名",
  emailAddress: "邮箱地址",
  enterYourEmail: "输入您的邮箱",
  editButton: "编辑...",
  saving: "保存中...",
  securitySettings: "安全设置",
  managePasswordSecurity: "管理您的密码和账户安全",
  currentPassword: "当前密码",
  enterCurrentPassword: "输入当前密码",
  newPassword: "新密码",
  enterNewPassword: "输入新密码",
  confirmNewPassword: "确认新密码",
  confirmNewPasswordPlaceholder: "确认新密码",
  updating: "更新中...",
  updatePassword: "更新密码",
  dangerZone: "危险区域",
  permanentlyDeleteAccount: "永久删除您的账户和所有相关数据",
  deleteAccountButton: "删除账户...",

  // Login page - new keys only
  newToAiCustomerService: "初次使用YapYapBot？",

  // Register page - new keys only
  alreadyHaveAccount: "已有账户？",

  // Project page - new keys only
  appearance: "外观",
  adjustAppearance: "调整聊天窗口的外观以匹配您网站的风格。",
  logo: "标志",
  clickToUpload: "上传",
  svgPngJpg: "SVG、PNG或JPG格式（最大800x400像素）",
  brandColor: "品牌颜色",
  suggestedQuestions: "建议问题",
  helpVisitorsStart: "通过提供快速的一键问题来帮助访客开始对话。",
  enterSuggestedQuestion: "输入建议问题...",
  addQuestion: "添加问题",
  projectIdMissing: "项目ID缺失，无法保存配置",
  addEmailAddress: "添加邮箱地址",
  enterEmailAddress: "请输入邮箱地址",
  emailAddresses: "邮箱地址",
  enableEmailNotifications: "启用邮箱通知",
  receiveEmailNotificationsForNewMessages: "在有新的聊天时接收邮件总结通知",
  customPrompt: "自定义会话总结提示词",
  enterCustomPromptPlaceholder: "请输入您自定义的提示词...",

  // Chat Records page - new keys only
  search: "搜索",
  loadingConversations: "加载对话中...",
  conversationDetails: "对话详情",
  id: "ID",
  channel: "渠道",
  customerInfo: "客户信息",
  browser: "浏览器",
  system: "系统",
  createdAt: "创建时间",
  sources: "来源：",
  referenceData: "参考资料",

  // Billing Plan page - new keys only
  billingPlan: "计费和套餐",
  managePlanBilling: "管理您的Quickset账户的套餐和计费设置",
  plans: "套餐",
  yearly: "年付",
  monthly: "月付",
  mostPopular: "最受欢迎",
  supportResponses: "支持响应/月",
  aiChatbots: "AI聊天机器人",
  sourcesForTraining: "每个聊天机器人的训练资源",
  seatsPerChatbot: "每个聊天机器人的席位",
  currentPlan: "当前套餐",
  upgrade: "升级",
  loadingPlans: "加载套餐中...",
  failedToFetchPlan: "获取套餐信息失败",
  unlockProFeatures: "解锁专业功能",
  renewalDate: "续费日期",
  notSet: "未设置",
  planLimitsUsage: "套餐限制使用情况",
  monthlyResponseCredits: "月度响应积分",
  additionalCredits: "额外积分",
  purchaseMore: "购买更多",
  botLimitsUsage: "机器人限制使用情况",
  sourcesForTrainingLimit: "训练资源",
  unableToLoadPlan: "无法加载套餐信息",

  // Current Product Info - new keys only
  failedToLoadProductInfo: "加载产品信息失败",
  unknownError: "未知错误",
  expired: "已过期",
  expiresInDays: "{days}天后过期",
  productExpired: "{productName}已过期",
  currentProduct: "您正在享受{productName}服务",
  productExpiredMessage: "您的订阅已过期，请续费以继续使用所有功能。",
  productExpiringSoonMessage:
    "您的订阅将于{expireTime}过期，请及时续费以避免服务中断。",
  productActiveUntil: "您的订阅有效期至{expireTime}。",
  renewNow: "立即续费",
  upgradeNow: "管理订阅",
  expiresOn: "过期时间：{date}",
  expiringSoon: "即将过期",

  // Documentation page - new keys only
  searchDocumentation: "搜索文档...",
  noMatchingDocuments: "未找到匹配的文档",
  documents: "文档",
  tableOfContents: "目录",
  selectDocument: "选择文档",
  chooseDocumentFromSidebar: "从左侧边栏选择一个文档查看",

  // NotFound page - new keys only
  pageNotFound: "哎呀！页面未找到",
  returnToHome: "返回首页",

  // OAuth Callback page - new keys only
  signingYouIn: "正在为您登录...",
  pleaseWaitAuthentication: "请稍候，我们正在完成您的身份验证",
  authenticationFailed: "身份验证失败",
  backToLogin: "返回登录",

  // Onboarding pages - new keys only
  websiteAddress: "您的网站地址是什么？",
  provideWebsiteUrl:
    "提供您的网站URL来帮助训练您的AI代理。我们将开始提取关键数据，让您的聊天机器人在几分钟内准备就绪。",
  enterWebsiteAddress: "输入您的网站地址",
  completeLater: "您可以稍后完成此步骤",
  customizeAiAgent: "自定义您的AI代理",
  matchWebsiteColors:
    "我们尝试匹配您网站的颜色、标志和品牌个性。请在下方自定义：",
  brandName: "品牌名称",
  aiAgentReady: "您的AI代理已准备就绪！",
  satisfiedCopyCode:
    "当您满意时，复制下面的代码并粘贴到您网站的</body>标签之前以激活它。",
  copied: "已复制！",
  copy: "复制",
  testAiAgent: "现在就测试您的AI代理吧。试试下面的示例：",
  testWithExamples: "用这些示例测试：",
  creating: "创建中...",
  startNow: "立即开始",

  // Test Install page - new keys only
  seeHowToInstall: "查看如何安装Web聊天窗口：",

  // KnowledgeBase page - new keys only
  title: "标题",
  lastTrained: "最后训练时间",
  actions: "操作",
  filter: "筛选",
  failedToLoadKnowledge: "加载知识库失败",
  selectedFiles: "已选择文件：",
  openLink: "打开链接",
  deleteItem: "删除项目",

  // Pagination
  showing: "显示第",
  to: "-",
  of: "条，共",
  items: "条",
  previousPage: "上一页",
  nextPage: "下一页",

  // Additional missing keys
  pageNotFoundDescription: "您要查找的页面不存在或已被移动。",

  // Mail Management
  mailManagement: "邮箱管理",
  addMail: "添加邮箱",
  mailType: "邮箱类型",
  senderName: "发件人姓名",
  authorizedEmail: "邮箱认证",
  clickToAuthorized: "点击认证",
  authorized: "已认证",
  unauthorized: "未认证",
  companyName: "公司名称",
  companyAddress: "公司地址",
  productHomepage: "产品主页",
  activated: "激活状态",
  type: "类型",
  sender: "发件人",
  connected: "已连接",
  disconnected: "未连接",
  basic: "基本信息",
  option: "选项",
  done: "完成",
  authorizationRequired: "需要授权",
  authorizationInProgress: "授权中，请检查服务",
  readComposePermission: "读取、撰写、发送和永久删除所有Gmail邮件",
  outlookHotmailNotSupported: "Outlook/Hotmail不支持子账户授权",
  pleaseEnterValidEmail: "请输入有效的邮箱地址",
  pleaseSelectMailType: "请选择邮箱类型",
  pleaseEnterSenderName: "请输入发件人姓名",
  authorizationSuccess: "授权成功",
  authorizationFailed: "授权失败",
  mailAddedSuccess: "邮箱添加成功",
  mailAddedFailed: "邮箱添加失败",

  // Form Builder
  formBuilder: "表单构建器",
  formList: "表单列表",
  formDesigner: "表单设计器",
  newForm: "新建表单",
  formTitle: "表单标题",
  formDescription: "表单描述",
  formLogo: "表单Logo",
  formTitlePlaceholder: "请输入表单标题",
  formDescriptionPlaceholder: "请输入表单描述",
  uploadLogo: "上传Logo",
  changeLogo: "更换Logo",
  removeLogo: "移除Logo",
  logoUploadTip: "支持 JPG、PNG 格式，建议尺寸 200x200px",
  logoUploadError: "Logo上传失败",
  logoUploadSuccess: "Logo上传成功",
  logoFormatError: "请选择 JPG 或 PNG 格式的图片",
  logoSizeError: "图片大小不能超过 2MB",
  uploadSuccess: "上传成功",
  saveForm: "保存表单",
  savingForm: "保存中...",
  previewForm: "预览表单",
  undo: "撤销",
  redo: "重做",
  loadSuccess: "加载成功",
  loadFailed: "加载失败",
  saveSuccess: "保存成功",
  deleteSuccess: "删除成功",
  deleteFailed: "删除失败",
  copySuccess: "复制成功",
  copyFailed: "复制失败",
  formLoadedSuccessfully: "表单已成功加载",
  formLoadFailed: "加载表单失败",
  formSavedSuccessfully: "表单保存成功",
  formSaveFailed: "表单保存失败",
  formSubmissions: "表单提交",
  totalSubmissions: "提交总数",
  noSubmissionsYet: "暂无提交",
  noSubmissionsDescription: "当用户提交此表单时，他们的回复将显示在这里。",
  loadSubmissionsFailed: "加载提交失败",
  showingPage: "第 {{currentPage}} 页，共 {{totalPages}} 页（总计 {{totalItems}} 项）",
  formDeletedSuccessfully: "表单已成功删除",
  formDeleteFailed: "删除表单失败",
  formCopiedSuccessfully: "表单已成功复制",
  formCopyFailed: "复制表单失败",
  previewFailed: "预览失败",
  pleaseFirstSaveForm: "请先保存表单",
  formManagement: "表单管理",
  createAndManageForms: "创建和管理您的表单",
  searchForms: "搜索表单...",
  noFormsFound: "未找到匹配的表单",
  noFormsYet: "还没有表单",
  tryDifferentSearch: "尝试使用不同的搜索词",
  createFirstForm: "创建您的第一个表单开始收集数据",
  components: "个组件",
  template: "模板",
  contactForm: "联系表单",
  registrationForm: "注册表单",
  feedbackForm: "反馈表单",
  templateCreatedSuccessfully: "模板创建成功",
  templateCreationFailed: "创建失败",
  templateCreated: "已创建",
  shareLink: "分享链接",
  linkCopied: "链接已复制",
  shareLinkCopied: "分享链接已复制到剪贴板",
  confirmDeleteForm: "确定要删除表单",
  deleteFormWarning: "吗？此操作无法撤销。",
  formNotExist: "表单不存在",
  loadFormFailed: "加载表单失败",
  getFormListFailed: "获取表单列表失败",
  activateForm: "激活表单",
  deactivateForm: "取消激活表单",

  // Form Designer Components
  componentLibrary: "组件库",
  searchComponents: "搜索组件...",
  basicComponents: "基础组件",
  layoutComponents: "布局组件",
  templateComponents: "常用组件",
  basic: "基础",
  layout: "布局",
  common: "常用",

  // Component Names
  textLabel: "文本标签",
  textInput: "文本输入",
  multilineText: "多行文本",
  dropdown: "下拉选择",
  radioButton: "单选按钮",
  checkbox: "复选框",
  datePicker: "日期选择",
  numberInput: "数字输入",
  emailInput: "邮箱输入",
  phoneInput: "电话输入",
  heading: "标题",
  paragraph: "段落",
  divider: "分割线",
  fullName: "姓名",
  contactInfo: "联系方式",
  address: "地址",
  idNumber: "身份证号",
  bankCard: "银行卡",

  // Component Descriptions
  singleLineTextInput: "单行文本输入框",
  multilineTextInput: "多行文本输入框",
  dropdownSelect: "下拉选择框",
  radioButtonGroup: "单选按钮组",
  checkboxGroup: "复选框组",
  datePickerComponent: "日期选择器",
  numberInputBox: "数字输入框",
  emailInputBox: "邮箱输入框",
  phoneInputBox: "电话号码输入框",
  headingText: "标题文本",
  paragraphText: "段落文本",
  dividerLine: "分割线",
  fullNameInput: "完整姓名输入",
  phoneAndEmailInput: "电话和邮箱输入",
  fullAddressInput: "地址输入",
  idNumberInput: "身份证号码输入",
  bankCardInput: "银行卡信息输入",

  // Placeholders
  enterText: "请输入文本",
  enterMultilineText: "请输入多行文本",
  pleaseSelect: "请选择",
  selectDate: "请选择日期",
  enterNumber: "请输入数字",
  enterIdNumber: "请输入身份证号码",

  // Default Values
  idCardNumber: "身份证号码",
  bankCardInfo: "银行卡信息",
  titleText: "标题文本",
  paragraphContent: "这是一段文本内容",

  // Property Panel
  selectComponent: "选择组件",
  clickComponentToEdit: "点击画布中的组件来编辑其属性",
  propertySettings: "属性设置",
  basicProperties: "基础属性",
  labelText: "标签文本",
  placeholder: "占位符",
  required: "必填项",
  enterLabelText: "请输入标签文本",
  enterPlaceholderText: "请输入占位符文本",

  // Heading Settings
  headingSettings: "标题设置",
  headingLevel: "标题级别",
  h1MainTitle: "H1 - 主标题",
  h2Subtitle: "H2 - 副标题",
  h3ThirdLevel: "H3 - 三级标题",
  h4FourthLevel: "H4 - 四级标题",
  h5FifthLevel: "H5 - 五级标题",
  h6SixthLevel: "H6 - 六级标题",

  // Button Settings
  buttonSettings: "按钮设置",
  buttonStyle: "按钮样式",
  primaryButton: "主要按钮",
  secondaryButton: "次要按钮",
  outlineButton: "边框按钮",
  ghostButton: "幽灵按钮",

  // Option Settings
  optionSettings: "选项设置",
  addOption: "添加选项",
  optionLabel: "选项标签",

  // Validation Rules
  validationRules: "验证规则",
  minLength: "最小长度",
  maxLength: "最大长度",
  unlimited: "无限制",
  emailValidation: "邮箱格式验证",
  phoneValidation: "手机号格式验证",

  // Style Settings
  styleSettings: "样式设置",
  width: "宽度",
  alignment: "对齐方式",
  leftAlign: "左对齐",
  centerAlign: "居中",
  rightAlign: "右对齐",

  // Design Canvas
  formTitleDefault: "表单标题",
  fillFollowingInfo: "请填写以下信息",
  startDesigning: "开始设计您的表单",
  dragComponentsHere: "从左侧组件库拖拽组件到这里开始设计",
  submit: "提交",
  copyLabel: "副本",

  // Form Preview
  formNotExistOrDeleted: "表单不存在或已被删除",
  formConfigNotExist: "表单配置不存在",
  loadingForm: "正在加载表单...",
  backToFormBuilder: "返回表单构建器",
  backToList: "返回表单列表",
  previewMode: "预览模式",
  formId: "表单ID",
  editForm: "编辑表单",
  copyForm: "复制表单",
  viewSubmissions: "查看提交",
  deleteForm: "删除表单",
  exportConfig: "导出配置",
  shareForm: "分享",
  formInfo: "表单信息",
  componentCount: "组件数量",
  componentsUnit: "个组件",
  createdTime: "创建时间",
  lastModified: "最后修改",
  formSettings: "表单设置",
  submitButtonText: "提交按钮文案",
  successMessage: "成功提示",
  errorMessage: "错误提示",
  allowMultipleSubmissions: "允许多次提交",
  requireLogin: "需要登录",
  collectEmail: "收集邮箱",
  yes: "是",
  no: "否",
  shareUrl: "分享链接",
  copyShareUrl: "复制分享链接",
  shareUrlCopied: "分享链接已复制到剪贴板",
  downloadConfig: "下载配置",
  submitSuccess: "提交成功",
  noProjectSelected: "未选择项目",
  submitSuccessMessage: "您的表单已成功提交，感谢您的参与！",
  submitFailed: "提交失败，请稍后重试",
  goHome: "返回首页",
  onlineForm: "在线表单",
  poweredBy: "由",
  formBuilderName: "表单构建器",
  techSupport: "提供技术支持",
  openInNewWindow:'在新的窗口打开',
  enableForm: "启用表单",
  collectUserFeedback: "通过表单收集用户反馈、联系方式等信息",
  existingFormLink: "现有表单链接",
  buildNewForm: "创建新表单",
  enableWhatsApp: "启用WhatsApp",
  communicateDirectlyWithCustomerServiceViaWhatsApp: "通过WhatsApp直接与客户服务沟通",
  pleaseEnterYourFormLinkAddress: "请输入您的表单链接地址",
  pleaseEnterYourWhatsAppLinkAddress: "请输入您的WhatsApp链接地址",
};
















