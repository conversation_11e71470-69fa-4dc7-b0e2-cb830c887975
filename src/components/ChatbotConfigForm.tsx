import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
  Plus,
  X,
  Upload,
  GripVertical,
  ExternalLink,
  Settings,
  FileText,
  MessageSquare,
  HelpCircle,
  ChevronDown,
  Mail
} from "lucide-react";
import { useNavigate } from "react-router-dom";

// Chatbot配置数据结构
export interface ChatbotConfig {
  brandName: string;
  brandColor: string;
  logo: string;
  welcomeMessage: string;
  suggestedQuestionsEnabled: boolean;
  suggestedQuestions: string[];
  formEnable: boolean;
  externalFormUrl: string;
  internalFormUrl: string;
  currentForm: number;
  whatsappEnable: boolean;
  whatsappAddress: string;
  notificationEnabled: boolean;
  notificationEmails: string[];
  customPrompt: string;
}

// Chatbot配置更新函数类型
export type UpdateChatbotConfig = <K extends keyof ChatbotConfig>(
  field: K,
  value: ChatbotConfig[K]
) => void;

interface ChatbotConfigFormProps {
  config: ChatbotConfig;
  updateConfig: UpdateChatbotConfig;
}

const ChatbotConfigForm = ({
  config,
  updateConfig,
}: ChatbotConfigFormProps) => {
  const { t } = useTranslation();
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const navigate = useNavigate();

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === "string") {
          updateConfig("logo", result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removeSuggestedQuestion = (index: number) => {
    const newQuestions = config.suggestedQuestions.filter((_, i) => i !== index);
    updateConfig("suggestedQuestions", newQuestions);
  };

  const addSuggestedQuestion = () => {
    const newQuestions = [...config.suggestedQuestions, ""];
    updateConfig("suggestedQuestions", newQuestions);
  };

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", index.toString());

    // 找到当前拖拽的整个item容器
    const draggedElement = e.currentTarget.closest(".draggable-item");
    if (draggedElement) {
      e.dataTransfer.setDragImage(draggedElement, 0, 0);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
    e.preventDefault();
    setDragOverIndex(null);

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newQuestions = [...config.suggestedQuestions];
    const draggedItem = newQuestions[draggedIndex];

    // Remove the dragged item
    newQuestions.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newQuestions.splice(insertIndex, 0, draggedItem);

    updateConfig("suggestedQuestions", newQuestions);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  return (
      <div className="space-y-8">
        <Accordion type="single" defaultValue="basic-settings" collapsible className="w-full">
          {/* Basic Settings */}
          <AccordionItem value="basic-settings" className="border-none">
            <AccordionTrigger className="px-0 py-4 hover:no-underline border-b border-gray-200 [&>svg]:hidden [&[data-state=open]>div>svg]:rotate-180">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <Settings className="w-5 h-5 text-blue-600" />
                  <span className="font-semibold text-gray-900 text-lg">Basic Settings</span>
                </div>
                <ChevronDown className="w-5 h-5 text-gray-500 transition-transform duration-200" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-6 pb-8">
              <div className="space-y-6">
                {/* Logo, Name and Brand Color in one row */}
                <div className="bg-gradient-to-br from-slate-50 to-gray-50 p-6 rounded-xl border border-gray-100">
                  <div className="flex items-center gap-8">
                    {/* Logo Section - 垂直居中 */}
                    <div className="flex-shrink-0 flex flex-col items-center">
                      <input
                          type="file"
                          id="logo-upload"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                      />
                      <div className="w-20 h-20 rounded-2xl bg-white shadow-sm flex items-center justify-center overflow-hidden border border-gray-200 relative group hover:shadow-md transition-shadow">
                        {config.logo ? (
                            <>
                              <img
                                  src={config.logo}
                                  alt="Logo"
                                  className="w-full h-full object-cover"
                              />
                              <label
                                  htmlFor="logo-upload"
                                  className="absolute inset-0 cursor-pointer flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-2xl"
                              >
                                <Upload className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                              </label>
                            </>
                        ) : (
                            <label
                                htmlFor="logo-upload"
                                className="cursor-pointer w-full h-full flex items-center justify-center hover:bg-gray-50 transition-colors rounded-2xl"
                            >
                              <div className="text-center">
                                <Upload className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                                <span className="text-xs text-gray-500 font-medium">Logo</span>
                              </div>
                            </label>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-2 text-center font-medium">
                        SVG, PNG, JPG
                      </p>
                      <p className="text-xs text-gray-500 mt-2 text-center font-medium">
                        (max. 800x400px)
                      </p>
                    </div>

                    {/* Name and Brand Color Section - 右移并优化布局 */}
                    <div className="flex-1 max-w-md space-y-5">
                      {/* Name */}
                      <div>
                        <Label htmlFor="brandName" className="text-sm font-semibold mb-2 block text-gray-800">
                          {t("name")}
                        </Label>
                        <div className="space-y-1">
                          <Input
                              id="brandName"
                              value={config.brandName}
                              onChange={(e) => updateConfig("brandName", e.target.value)}
                              placeholder="YapYapBot"
                              className="w-64 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 focus:outline-none rounded-lg"
                              maxLength={20}
                          />
                          <div className="text-xs text-gray-500 text-right font-medium w-64">
                            {config.brandName.length}/20
                          </div>
                        </div>
                      </div>

                      {/* Brand Color */}
                      <div>
                        <Label htmlFor="brandColor" className="text-sm font-semibold mb-2 block text-gray-800">
                          {t("brandColor")}
                        </Label>
                        <div className="flex items-center space-x-3">
                          <Input
                              id="brandColor"
                              value={config.brandColor}
                              onChange={(e) => updateConfig("brandColor", e.target.value)}
                              className="w-64 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 focus:outline-none rounded-lg font-mono text-sm"
                          />
                          <input
                              type="color"
                              value={config.brandColor}
                              onChange={(e) => updateConfig("brandColor", e.target.value)}
                              className="w-10 h-10 rounded-lg border-2 border-gray-300 cursor-pointer shadow-sm hover:shadow-md transition-shadow"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Welcome message */}
                <div>
                  <Label htmlFor="welcomeMessage" className="text-sm font-semibold mb-3 block text-gray-800">
                    {t("welcomeMessage")}
                  </Label>
                  <Textarea
                      id="welcomeMessage"
                      value={config.welcomeMessage}
                      onChange={(e) => updateConfig("welcomeMessage", e.target.value)}
                      rows={4}
                      className="resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 focus:outline-none rounded-lg"
                      placeholder="Enter a welcoming message for your visitors..."
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Form Settings */}
          <AccordionItem value="form-settings" className="border-none">
            <AccordionTrigger className="px-0 py-4 hover:no-underline border-b border-gray-200 [&>svg]:hidden [&[data-state=open]>div>svg]:rotate-180">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5 text-green-600" />
                  <span className="font-semibold text-gray-900 text-lg">Form Settings</span>
                </div>
                <ChevronDown className="w-5 h-5 text-gray-500 transition-transform duration-200" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-6 pb-8">
              <div className="space-y-5">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-semibold text-gray-800">
                      {t("enableForm")}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {t("collectUserFeedback")}
                    </p>
                  </div>
                  <Switch
                      checked={config.formEnable}
                      onCheckedChange={(checked) => updateConfig("formEnable", checked)}
                  />
                </div>

                {config.formEnable && (
                    <div className="space-y-4">
                      <div className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                        <div className="flex items-start space-x-3">
                          <input
                              type="radio"
                              id="existingForm"
                              name="formType"
                              checked={config.currentForm === 1}
                              onChange={() => updateConfig("currentForm", 1)}
                              className="mt-1 text-blue-600"
                          />
                          <div className="flex-1">
                            <Label htmlFor="existingForm" className="text-sm font-medium text-gray-800">
                              {t("existingFormLink")}
                            </Label>
                            <Input
                                value={config.externalFormUrl}
                                onChange={(e) => updateConfig("externalFormUrl", e.target.value)}
                                placeholder={t("pleaseEnterYourFormLinkAddress")}
                                disabled={config.currentForm !== 1}
                                className="mt-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 focus:outline-none rounded-lg"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <input
                                type="radio"
                                id="buildNewForm"
                                name="formType"
                                checked={config.currentForm === 0}
                                onChange={() => updateConfig("currentForm", 0)}
                                className="text-blue-600"
                            />
                            <Label htmlFor="buildNewForm" className="text-sm font-medium text-gray-800">
                              {t("buildNewForm")}
                            </Label>
                          </div>
                          <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate("/form-builder")}
                              className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
                          >
                            {t("formManagement")}
                            <ExternalLink className="w-4 h-4 ml-1" />
                          </Button>
                        </div>
                      </div>
                    </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* WhatsApp Settings */}
          <AccordionItem value="whatsapp-settings" className="border-none">
            <AccordionTrigger className="px-0 py-4 hover:no-underline border-b border-gray-200 [&>svg]:hidden [&[data-state=open]>div>svg]:rotate-180">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <MessageSquare className="w-5 h-5 text-green-600" />
                  <span className="font-semibold text-gray-900 text-lg">WhatsApp Settings</span>
                </div>
                <ChevronDown className="w-5 h-5 text-gray-500 transition-transform duration-200" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-6 pb-8">
              <div className="space-y-5">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-semibold text-gray-800">
                      {t("enableWhatsApp")}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {t("communicateDirectlyWithCustomerServiceViaWhatsApp")}
                    </p>
                  </div>
                  <Switch
                      checked={config.whatsappEnable}
                      onCheckedChange={(checked) => updateConfig("whatsappEnable", checked)}
                  />
                </div>

                {config.whatsappEnable && (
                    <div>
                      <div className="flex items-center border border-gray-300 rounded-lg focus-within:border-green-500 focus-within:ring-1 focus-within:ring-green-500">
                      <span className="px-3 py-2 bg-gray-50 text-gray-700 text-sm border-r border-gray-300 rounded-l-lg">
                        https://wa.me/
                      </span>
                        <Input
                            value={config.whatsappAddress ? config.whatsappAddress.replace('https://wa.me/', '') : ''}
                            onChange={(e) => {
                              const phoneNumber = e.target.value;
                              const fullUrl = phoneNumber ? `https://wa.me/${phoneNumber}` : '';
                              updateConfig("whatsappAddress", fullUrl);
                            }}
                            placeholder={t("pleaseEnterYourWhatsAppLinkAddress")}
                            className="border-0 focus:ring-0 focus:outline-none rounded-r-lg flex-1"
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {"Enter phone number with country code (e.g., 14385480077 for Canada)"}
                      </p>
                    </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Suggested Settings */}
          <AccordionItem value="suggested-settings" className="border-none">
            <AccordionTrigger className="px-0 py-4 hover:no-underline border-b border-gray-200 [&>svg]:hidden [&[data-state=open]>div>svg]:rotate-180">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <HelpCircle className="w-5 h-5 text-purple-600" />
                  <span className="font-semibold text-gray-900 text-lg">Suggested Settings</span>
                </div>
                <ChevronDown className="w-5 h-5 text-gray-500 transition-transform duration-200" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-6 pb-8">
              <div className="space-y-5">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-semibold text-gray-800">
                      {t("suggestedQuestions")}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {t("helpVisitorsStart")}
                    </p>
                  </div>
                  <Switch
                      checked={config.suggestedQuestionsEnabled}
                      onCheckedChange={(checked) => updateConfig("suggestedQuestionsEnabled", checked)}
                  />
                </div>

                {config.suggestedQuestionsEnabled && (
                    <div className="space-y-3">
                      {config.suggestedQuestions.map((question, index) => (
                          <div
                              key={index}
                              className={`flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg transition-all draggable-item hover:shadow-sm ${
                                  dragOverIndex === index
                                      ? "bg-blue-50 border-blue-300 shadow-md"
                                      : draggedIndex === index
                                          ? "opacity-50"
                                          : ""
                              }`}
                              draggable
                              onDragStart={(e) => handleDragStart(e, index)}
                              onDragOver={(e) => handleDragOver(e, index)}
                              onDragLeave={handleDragLeave}
                              onDrop={(e) => handleDrop(e, index)}
                              onDragEnd={handleDragEnd}
                          >
                            <GripVertical className="w-4 h-4 text-gray-400 cursor-grab hover:text-gray-600 active:cursor-grabbing flex-shrink-0" />
                            <Input
                                value={question}
                                onChange={(e) => {
                                  const newQuestions = [...config.suggestedQuestions];
                                  newQuestions[index] = e.target.value;
                                  updateConfig("suggestedQuestions", newQuestions);
                                }}
                                className="flex-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 focus:ring-1 focus:outline-none rounded-lg"
                                placeholder={t("enterSuggestedQuestion")}
                            />
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeSuggestedQuestion(index)}
                                className="hover:bg-red-50 hover:text-red-600 flex-shrink-0 rounded-lg"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                      ))}

                      {/* Add Question Button */}
                      <div className="flex justify-center pt-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={addSuggestedQuestion}
                            className="border-dashed border-2 border-gray-300 hover:border-purple-400 hover:bg-purple-50 text-gray-600 hover:text-purple-600 rounded-lg px-6"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          {t("addQuestion")}
                        </Button>
                      </div>
                    </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Notification Settings */}
          <AccordionItem value="notification-settings" className="border-none">
            <AccordionTrigger className="px-0 py-4 hover:no-underline border-b border-gray-200 [&>svg]:hidden [&[data-state=open]>div>svg]:rotate-180">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-orange-600" />
                  <span className="font-semibold text-gray-900 text-lg">Notification Settings</span>
                </div>
                <ChevronDown className="w-5 h-5 text-gray-500 transition-transform duration-200" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-0 pt-6 pb-8">
              <div className="space-y-6">
                {/* 邮箱通知设置 */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-semibold text-gray-800">
                      {t("enableEmailNotifications")}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {t("receiveEmailNotificationsForNewMessages")}
                    </p>
                  </div>
                  <Switch
                      checked={config.notificationEnabled}
                      onCheckedChange={(checked) => updateConfig("notificationEnabled", checked)}
                  />
                </div>

                {config.notificationEnabled && (
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-semibold mb-3 block text-gray-800">
                          {t("emailAddresses")} (Max 3)
                        </Label>
                        <div className="space-y-3">
                          {config.notificationEmails && config.notificationEmails.length > 0 ?
                              config.notificationEmails.map((email, index) => (
                                  <div
                                      key={index}
                                      className="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-all"
                                  >
                                    <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                    <Input
                                        type="email"
                                        value={email}
                                        onChange={(e) => {
                                          const newEmails = [...config.notificationEmails];
                                          newEmails[index] = e.target.value;
                                          updateConfig("notificationEmails", newEmails);
                                        }}
                                        className="flex-1 border-gray-300 focus:border-orange-500 focus:ring-orange-500 focus:ring-1 focus:outline-none rounded-lg"
                                        placeholder={t("enterEmailAddress")}
                                    />
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          const newEmails = config.notificationEmails.filter((_, i) => i !== index);
                                          updateConfig("notificationEmails", newEmails);
                                        }}
                                        className="hover:bg-red-50 hover:text-red-600 flex-shrink-0 rounded-lg"
                                    >
                                      <X className="w-4 h-4" />
                                    </Button>
                                  </div>
                              )) : (
                                  <div className="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg">
                                    <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                    <Input
                                        type="email"
                                        value=""
                                        onChange={(e) => {
                                          if (e.target.value) {
                                            updateConfig("notificationEmails", [e.target.value]);
                                          }
                                        }}
                                        className="flex-1 border-gray-300 focus:border-orange-500 focus:ring-orange-500 focus:ring-1 focus:outline-none rounded-lg"
                                        placeholder={t("enterEmailAddress")}
                                    />
                                  </div>
                              )
                          }
                        </div>

                        {/* Add Email Button - 只有在邮箱数量小于3时才显示 */}
                        {(!config.notificationEmails || config.notificationEmails.length < 3) && (
                            <div className="flex justify-center pt-3">
                              <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const currentEmails = config.notificationEmails || [];
                                    if (currentEmails.length < 3) {
                                      updateConfig("notificationEmails", [...currentEmails, ""]);
                                    }
                                  }}
                                  className="border-dashed border-2 border-gray-300 hover:border-orange-400 hover:bg-orange-50 text-gray-600 hover:text-orange-600 rounded-lg px-6"
                              >
                                <Plus className="w-4 h-4 mr-2" />
                                {t("addEmailAddress")}
                              </Button>
                            </div>
                        )}
                      </div>
                    </div>
                )}

                {/* 自定义Prompt设置 */}
                {config.notificationEnabled && (
                    <div className="border-t border-gray-200 pt-6">
                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm font-semibold mb-3 block text-gray-800">
                            {t("customPrompt")}
                          </Label>
                        </div>

                        <div className="relative">
                          <textarea
                              value={config.customPrompt || ""}
                              onChange={(e) => {
                                if (e.target.value.length <= 500) {
                                  updateConfig("customPrompt", e.target.value);
                                }
                              }}
                              className="w-full min-h-[120px] p-4 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-orange-500 focus:ring-1 focus:outline-none resize-y"
                              placeholder={t("enterCustomPromptPlaceholder") || "请输入您的自定义Prompt，例如：请以友好的语调回复用户消息..."}
                              rows={5}
                              maxLength={500}
                          />
                          <div className={`absolute bottom-3 right-3 text-xs ${
                              (config.customPrompt || "").length >= 500 ? 'text-red-500' : 'text-gray-400'
                          }`}>
                            {(config.customPrompt || "").length}/500
                          </div>
                        </div>

                        {/* Prompt预设选项 */}
                        <div>
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateConfig("customPrompt", "You are an AI assistant that creates warm, " +
                                    "concise summaries of customer conversations for business owners. " +
                                    "Please write the summary in short, natural sentences that sound like a helpful update rather than a formal report. " +
                                    "Focus on what the customer asked, how the agent responded, and what still needs attention. " +
                                    "Mention if follow-up is needed, in a supportive tone. Include the customer’s mood naturally. " +
                                    "Keep it to 5–7 sentences, like a quick email update.")}
                                className="justify-start text-left h-auto p-3 hover:bg-orange-50 hover:border-orange-300"
                            >
                              <div className="text-xs">
                                <div className="font-medium text-gray-800">Friendly & Action-Oriented</div>
                                <div className="text-gray-500">Warm and conversational</div>
                              </div>
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateConfig("customPrompt", "Summarize the customer conversation clearly and professionally. " +
                                    "Start with the main inquiry, then outline the agent’s response, any resolution or action taken, " +
                                    "and whether follow-up is required. Include the customer’s sentiment (e.g., satisfied, concerned). " +
                                    "Keep the tone neutral yet helpful, and limit the summary to 6–8 concise sentences. Avoid markdown or bullet points.")}
                                className="justify-start text-left h-auto p-3 hover:bg-orange-50 hover:border-orange-300"
                            >
                              <div className="text-xs">
                                <div className="font-medium text-gray-800">Professional & Structured</div>
                                <div className="text-gray-500">Clear and organized</div>
                              </div>
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateConfig("customPrompt", "Create a thoughtful and empathetic summary of the customer interaction. " +
                                    "Focus on the customer’s feelings, key concerns, and how the agent addressed them. " +
                                    "Highlight any emotional cues (e.g., frustration, relief) and the overall outcome. " +
                                    "Write in a warm, conversational style—like a caring team member updating the owner. " +
                                    "Keep it to 5–7 flowing sentences, no lists.")}
                                className="justify-start text-left h-auto p-3 hover:bg-orange-50 hover:border-orange-300"
                            >
                              <div className="text-xs">
                                <div className="font-medium text-gray-800">Empathetic & Insightful</div>
                                <div className="text-gray-500">Highlights emotion</div>
                              </div>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
  );
};

export default ChatbotConfigForm;