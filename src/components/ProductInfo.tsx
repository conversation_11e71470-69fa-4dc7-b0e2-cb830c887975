import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertCircle, Clock, Crown } from "lucide-react";
import { useCurrentProduct } from "@/hooks/useCurrentProduct";
import { useTranslation } from "@/hooks/useTranslation";

interface ProductInfoProps {
  variant?: "dashboard" | "account";
  className?: string;
}

export const ProductInfo = ({
  variant = "dashboard",
  className = "",
}: ProductInfoProps) => {
  const { currentProduct, loading, error } = useCurrentProduct();
  const { t } = useTranslation();

  // 格式化过期时间
  const formatExpireTime = (expireTime: string) => {
    const expireDate = new Date(expireTime);
    const now = new Date();
    const diffInMs = expireDate.getTime() - now.getTime();
    const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays <= 0) {
      return {
        text: t("expired"),
        isExpired: true,
        daysLeft: 0,
      };
    } else if (diffInDays <= 7) {
      return {
        text: t("expiresInDays", { days: diffInDays }),
        isExpired: false,
        daysLeft: diffInDays,
        isExpiringSoon: true,
      };
    } else {
      return {
        text: expireDate.toLocaleString(),
        isExpired: false,
        daysLeft: diffInDays,
        isExpiringSoon: false,
      };
    }
  };

  if (loading) {
    return (
      <Card className={`${className} animate-pulse`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !currentProduct) {
    return (
      <Card className={`${className} border-red-200 bg-red-50`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <div className="flex-1">
              <p className="text-sm text-red-800 font-medium">
                {t("failedToLoadProductInfo")}
              </p>
              <p className="text-xs text-red-600 mt-1">
                {error || t("unknownError")}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const expireInfo = formatExpireTime(currentProduct.expireTime);

  return (
    <Card
      className={`${className} ${
        expireInfo.isExpired
          ? "border-red-200 bg-red-50"
          : expireInfo.isExpiringSoon
          ? "border-orange-200 bg-orange-50"
          : "border-blue-200 bg-blue-50"
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div
            className={`w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${
              expireInfo.isExpired
                ? "bg-red-500"
                : expireInfo.isExpiringSoon
                ? "bg-orange-500"
                : "bg-blue-500"
            }`}
          >
            {expireInfo.isExpired ? (
              <AlertCircle className="w-3 h-3 text-white" />
            ) : (
              <Crown className="w-3 h-3 text-white" />
            )}
          </div>
          <div className="flex-1">
            <p
              className={`text-sm font-medium ${
                expireInfo.isExpired
                  ? "text-red-800"
                  : expireInfo.isExpiringSoon
                  ? "text-orange-800"
                  : "text-blue-800"
              }`}
            >
              {expireInfo.isExpired ? (
                <span>
                  {t("productExpired", {
                    productName: currentProduct.productName,
                  })}
                </span>
              ) : (
                <span>
                  {t("currentProduct", {
                    productName: currentProduct.productName,
                  })}{" "}
                  🎉
                </span>
              )}
            </p>
            <p
              className={`text-sm mt-1 ${
                expireInfo.isExpired
                  ? "text-red-700"
                  : expireInfo.isExpiringSoon
                  ? "text-orange-700"
                  : "text-blue-700"
              }`}
            >
              {expireInfo.isExpired ? (
                <>
                  {t("productExpiredMessage")}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-red-700 underline"
                  >
                    {t("renewNow")}
                  </Button>
                </>
              ) : expireInfo.isExpiringSoon ? (
                <>
                  {t("productExpiringSoonMessage", {
                    expireTime: expireInfo.text,
                  })}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-orange-700 underline"
                  >
                    {t("renewNow")}
                  </Button>
                </>
              ) : (
                <>
                  {t("productActiveUntil", { expireTime: expireInfo.text })}{" "}
                  <a href="/billing">
                    <Button
                      variant="link"
                      className="p-0 h-auto text-blue-700 underline"
                    >
                      {t("upgradeNow")}
                    </Button>
                  </a>
                </>
              )}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
