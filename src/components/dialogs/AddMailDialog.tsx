import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Info, Loader2 } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useToast } from "@/hooks/use-toast";
import { 
  checkEmailAuthorization, 
  addEmail, 
  MailType, 
  AddEmailRequest 
} from "@/services/api";
import { useProject } from "@/context/ProjectContext";

interface AddMailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const AddMailDialog: React.FC<AddMailDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { currentProject } = useProject();

  // 表单状态
  const [formData, setFormData] = useState({
    type: "" as MailType | "",
    sender: "",
    email: "",
    companyName: "",
    companyAddress: "",
    productPage: "",
    contactUs: "",
  });

  // 认证状态
  const [authStatus, setAuthStatus] = useState<"idle" | "checking" | "authorized" | "unauthorized">("idle");
  const [authUrl, setAuthUrl] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 重置表单
  const resetForm = () => {
    setFormData({
      type: "",
      sender: "",
      email: "",
      companyName: "",
      companyAddress: "",
      productPage: "",
      contactUs: "",
    });
    setAuthStatus("idle");
    setAuthUrl("");
  };

  // 关闭对话框
  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  // 验证基本信息是否完整
  const isBasicInfoValid = () => {
    return formData.type && formData.sender.trim() && formData.email.trim() && isValidEmail(formData.email);
  };

  // 验证邮箱格式
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 检查邮箱认证状态
  const handleCheckAuthorization = async () => {
    if (!isBasicInfoValid()) {
      if (!formData.type) {
        toast({
          title: t("error"),
          description: t("pleaseSelectMailType"),
          variant: "destructive",
        });
        return;
      }
      if (!formData.sender.trim()) {
        toast({
          title: t("error"),
          description: t("pleaseEnterSenderName"),
          variant: "destructive",
        });
        return;
      }
      if (!formData.email.trim() || !isValidEmail(formData.email)) {
        toast({
          title: t("error"),
          description: t("pleaseEnterValidEmail"),
          variant: "destructive",
        });
        return;
      }
    }

    setAuthStatus("checking");
    try {
      const result = await checkEmailAuthorization(formData.email);
      
      if (result === "true") {
        setAuthStatus("authorized");
        toast({
          title: t("success"),
          description: t("authorizationSuccess"),
        });
      } else {
        setAuthStatus("unauthorized");
        setAuthUrl(result);
        // 打开授权窗口
        window.open(result, "_blank", "width=600,height=600");
      }
    } catch (error) {
      console.error("Authorization check failed:", error);
      setAuthStatus("idle");
      toast({
        title: t("error"),
        description: t("authorizationFailed"),
        variant: "destructive",
      });
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!currentProject?.id) {
      toast({
        title: t("error"),
        description: "Project ID missing",
        variant: "destructive",
      });
      return;
    }

    if (!isBasicInfoValid()) {
      toast({
        title: t("error"),
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (authStatus !== "authorized") {
      toast({
        title: t("error"),
        description: t("authorizationRequired"),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const emailData: AddEmailRequest = {
        projectId: currentProject.id,
        type: formData.type as MailType,
        sender: formData.sender,
        email: formData.email,
        companyName: formData.companyName,
        companyAddress: formData.companyAddress,
        productPage: formData.productPage,
        contactUs: formData.contactUs,
      };

      await addEmail(emailData);
      
      toast({
        title: t("success"),
        description: t("mailAddedSuccess"),
      });
      
      handleClose();
      onSuccess?.();
    } catch (error) {
      console.error("Failed to add email:", error);
      toast({
        title: t("error"),
        description: t("mailAddedFailed"),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 渲染认证状态
  const renderAuthStatus = () => {
    switch (authStatus) {
      case "checking":
        return (
          <Button disabled className="w-auto">
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            {t("loading")}
          </Button>
        );
      case "authorized":
        return <Badge variant="default" className="bg-green-500">{t("authorized")}</Badge>;
      case "unauthorized":
        return <Badge variant="destructive">{t("unauthorized")}</Badge>;
      default:
        return (
          <Button 
            onClick={handleCheckAuthorization}
            disabled={!isBasicInfoValid()}
            variant="outline"
            className="w-auto"
          >
            {t("clickToAuthorized")}
          </Button>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {t("addMail")}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">{t("basic")}</h3>
            <div className="space-y-4">
              {/* Mail Type */}
              <div>
                <Label htmlFor="type">{t("mailType")}</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: MailType) => setFormData(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("pleaseSelect")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gmail">Gmail</SelectItem>
                    <SelectItem value="Outlook">Outlook</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sender Name */}
              <div>
                <Label htmlFor="sender">{t("senderName")}</Label>
                <Input
                  id="sender"
                  value={formData.sender}
                  onChange={(e) => setFormData(prev => ({ ...prev, sender: e.target.value }))}
                  placeholder={t("pleaseEnterSenderName")}
                />
              </div>

              {/* Email Address */}
              <div>
                <Label htmlFor="email">{t("emailAddress")}</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder={t("pleaseEnterValidEmail")}
                />
              </div>

              {/* Authorization */}
              <div>
                <Label>{t("authorizedEmail")}</Label>
                <div className="flex items-center gap-3 mt-2">
                  {renderAuthStatus()}
                </div>
                <div className="flex items-start gap-2 mt-2 p-3 bg-gray-50 rounded-lg">
                  <Info className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-gray-600">
                    <p>{t("authorizationInProgress")}</p>
                    <p>"{t("readComposePermission")}"</p>
                    <p>{t("outlookHotmailNotSupported")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Option Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">{t("option")}</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="companyName">{t("companyName")}</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="companyAddress">{t("companyAddress")}</Label>
                <Input
                  id="companyAddress"
                  value={formData.companyAddress}
                  onChange={(e) => setFormData(prev => ({ ...prev, companyAddress: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="productPage">{t("productHomepage")}</Label>
                <Input
                  id="productPage"
                  value={formData.productPage}
                  onChange={(e) => setFormData(prev => ({ ...prev, productPage: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="contactUs">{t("contactUs")}</Label>
                <Input
                  id="contactUs"
                  value={formData.contactUs}
                  onChange={(e) => setFormData(prev => ({ ...prev, contactUs: e.target.value }))}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t("cancel")}
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!isBasicInfoValid() || authStatus !== "authorized" || isSubmitting}
          >
            {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {t("done")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
