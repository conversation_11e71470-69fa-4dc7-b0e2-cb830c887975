import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Upload, Loader2 } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { TranslationKey } from "@/i18n/translations";
import { Input } from "../ui/input";
import { format } from "path";

interface UploadFileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpload: (files: FileList) => Promise<void>;
  accept?: string;
  multiple?: boolean;
  title?: string;
  description?: string;
  uploading?: boolean;
}

export const UploadFileDialog = ({
  open,
  onOpenChange,
  onUpload,
  accept = ".pdf,.doc,.docx,.txt,.md",
  multiple = true,
  title,
  description,
  uploading = false,
}: UploadFileDialogProps) => {
  const { t } = useTranslation();
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    setSelectedFiles(files);
  };

  const handleSubmit = async () => {
    if (selectedFiles && selectedFiles.length > 0) {
      await onUpload(selectedFiles);
      setSelectedFiles(null);
      onOpenChange(false);
    }
  };

  const handleClose = () => {
    setSelectedFiles(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title || t("uploadDocument")}</DialogTitle>
          <DialogDescription>
            {description || t("selectFiles")}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="document-upload">{t("selectFiles")}</Label>
            <Input
              id="document-upload"
              type="file"
              multiple
              accept={accept}
              onChange={handleFileSelect}
              disabled={uploading}
              className="cursor-pointer disabled:cursor-not-allowed"
            />
            <p className="text-xs text-gray-500 mt-1">
              {t("supportedFileTypes", { format: accept.replace(/,/g, ", ") })}
            </p>
            {/* {selectedFiles && selectedFiles.length > 0 && (
              <div className="mt-2">
                <p className="text-sm text-gray-600">
                  {t("selectedFiles")} {selectedFiles.length}
                </p>
                <ul className="text-xs text-gray-500 mt-1">
                  {Array.from(selectedFiles).map((file, index) => (
                    <li key={index}>{file.name}</li>
                  ))}
                </ul>
              </div>
            )} */}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={uploading}>
            {t("cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!selectedFiles || selectedFiles.length === 0 || uploading}
          >
            {uploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {t("uploading")}
              </>
            ) : (
              t("upload")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
