import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Bot,
  LayoutDashboard,
  MessageSquare,
  LogOut,
  User,
  ChevronDown,
  Settings,
  CreditCard,
  FileText,
  Phone,
  Mail,
  Mic,
  Workflow,
  Database,
  GitBranch,
  Plus,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { truncateText } from "@/utils/common.ts";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, logout, isLoading } = useAuth();
  const { currentProject, projects, setCurrentProject } = useProject();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // 菜单数据结构
  const menuData = [
    {
      title: t("dashboard"),
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: t("channels"),
      icon: MessageSquare,
      items: [
        {
          title: t("chatbot"),
          url: "/chats",
          icon: Bot,
        },
        {
          title: t("whatsapp"),
          url: "",
          icon: Phone,
        },
        {
          title: t("voice"),
          url: "",
          icon: Mic,
        },
        {
          title: t("mail"),
          url: "/channels/mail",
          icon: Mail,
        },
      ],
    },
    {
      title: t("knowledgeBase"),
      url: "/knowledge",
      icon: Database,
    },
    {
      title: t("workflow"),
      icon: Workflow,
      items: [
        {
          title: t("form"),
          url: "/form-builder",
          icon: FileText,
        },
        {
          title: t("pipeline"),
          url: "",
          icon: GitBranch,
        },
      ],
    },
    {
      title: t("chats"),
      url: "/chat-records",
      icon: MessageSquare,
    },
  ];

  const isActive = (href: string) => location.pathname === href;

  return (
      <SidebarProvider>
        <Sidebar variant="inset" className="bg-gradient-sidebar border-sidebar-border">
          <SidebarHeader className="border-b border-sidebar-border/50 bg-sidebar/50 backdrop-blur-sm">
            <SidebarMenu>
              <SidebarMenuItem>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="w-full justify-start px-3 py-3 h-auto hover:bg-sidebar-hover/50 transition-all duration-200 focus:outline-none focus:ring-0 focus:border-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    >
                      <div className="flex items-center w-full relative">
                        <div className="w-8 h-8 p-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                          <Bot className="h-5 w-5 text-white" />
                        </div>
                        <span className="absolute left-1/2 -translate-x-1/2 text-lg font-semibold text-sidebar-foreground">
                          {truncateText(currentProject?.name ?? "YapYapBot", 9)}
                        </span>
                        <ChevronDown className="h-4 w-4 text-sidebar-muted-foreground ml-auto" />
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                      align="start"
                      className="w-60 bg-white border border-gray-200 shadow-lg rounded-lg p-0 z-50"
                      sideOffset={8}
                  >
                    {/* Header with My bots and count */}
                    <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
                      <span className="text-sm font-medium text-gray-700">
                        My bots ({projects.length})
                      </span>
                    </div>

                    {/* Bot list with fixed height and scroll */}
                    <div className="py-2 max-h-[280px] overflow-y-auto">
                      {projects.map((project) => (
                          <DropdownMenuItem
                              key={project.id}
                              onClick={() => setCurrentProject(project)}
                              className={`mx-2 my-1 px-3 py-2.5 rounded-lg cursor-pointer transition-colors ${
                                  currentProject?.id === project.id
                                      ? "bg-blue-50 border border-blue-200"
                                      : "hover:bg-gray-50"
                              }`}
                          >
                            <div className="flex items-center w-full">
                              <div className="w-8 h-8 bg-white-500 rounded-lg flex items-center justify-center mr-3">
                                <Bot className="h-7 w-7 text-blue-600" />
                              </div>
                              <span className={`text-sm font-medium ${
                                  currentProject?.id === project.id
                                      ? "text-blue-700"
                                      : "text-gray-700"
                              }`}>
                                {project.name}
                              </span>
                            </div>
                          </DropdownMenuItem>
                      ))}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarHeader>
          <div className="sidebar-divider"></div>
          <SidebarContent className="px-2">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-2">
                  {menuData.map((item) => (
                      <Collapsible
                          key={item.title}
                          asChild
                          defaultOpen={item.items?.some(subItem => isActive(subItem.url))}
                          className="group/collapsible"
                      >
                        <SidebarMenuItem className="sidebar-menu-item">
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton
                                tooltip={item.title}
                                isActive={item.url ? isActive(item.url) : false}
                                asChild={!!item.url}
                                className="rounded-lg mx-1 hover:bg-sidebar-hover transition-all duration-200 group h-12 text-base font-normal !justify-start !pl-3"
                            >
                              {item.url ? (
                                  <Link to={item.url} className="flex items-center w-full !justify-start">
                                    <item.icon className="h-5 w-5 text-sidebar-accent-foreground group-hover:text-sidebar-primary transition-colors duration-200 !mr-3 !ml-0" />
                                    <span className={`text-base ${item.url && isActive(item.url) ? 'font-semibold' : 'font-normal'}`}>{item.title}</span>
                                  </Link>
                              ) : (
                                  <div className="flex items-center w-full !justify-start">
                                    <item.icon className="h-5 w-5 text-sidebar-accent-foreground group-hover:text-sidebar-primary transition-colors duration-200 !mr-3 !ml-0" />
                                    <span className={`text-base flex-1 text-left ${!item.url && item.items?.some(subItem => isActive(subItem.url)) ? 'font-semibold' : 'font-normal'}`}>{item.title}</span>
                                    {item.items && <ChevronDown className="h-4 w-4 text-sidebar-muted-foreground transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />}
                                  </div>
                              )}
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          {item.items && (
                              <CollapsibleContent>
                                <SidebarMenuSub className="ml-6 mt-2 space-y-2">
                                  {item.items.map((subItem) => (
                                      <SidebarMenuSubItem key={subItem.title}>
                                        <SidebarMenuSubButton
                                            asChild
                                            isActive={isActive(subItem.url)}
                                            className="rounded-lg hover:bg-sidebar-hover transition-all duration-200 group h-10 !justify-start !pl-3"
                                        >
                                          <Link to={subItem.url} className="flex items-center w-full !justify-start">
                                            <subItem.icon className="h-4 w-4 text-sidebar-muted-foreground group-hover:text-sidebar-accent-foreground transition-colors duration-200 !mr-3 !ml-0" />
                                            <span className={`text-sm ${isActive(subItem.url) ? 'font-semibold' : 'font-normal'}`}>{subItem.title}</span>
                                          </Link>
                                        </SidebarMenuSubButton>
                                      </SidebarMenuSubItem>
                                  ))}
                                </SidebarMenuSub>
                              </CollapsibleContent>
                          )}
                        </SidebarMenuItem>
                      </Collapsible>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <div className="sidebar-divider"></div>
          <SidebarFooter className="border-t border-sidebar-border/50 bg-sidebar/30 backdrop-blur-sm">
            {isLoading ? (
                // 加载状态
                <div className="flex items-center p-3 mx-2 rounded-lg">
                  <div className="w-10 h-10 bg-sidebar-muted rounded-full flex items-center justify-center animate-pulse">
                    <User className="w-5 h-5 text-sidebar-muted-foreground" />
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="h-4 bg-sidebar-muted rounded animate-pulse mb-2 w-20"></div>
                    <div className="h-3 bg-sidebar-muted/60 rounded animate-pulse w-16"></div>
                  </div>
                </div>
            ) : (
                // 用户信息下拉菜单
                <SidebarMenu>
                  <SidebarMenuItem>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground ml-1 mr-2 rounded-lg hover:bg-sidebar-hover transition-all duration-200 group h-14"
                        >
                          <div className="relative w-9 h-9 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-bold shadow-elegant">
                            {user?.name ? (
                                user.name.charAt(0).toUpperCase()
                            ) : (
                                <User className="w-4 h-4" />
                            )}
                            <div className="absolute -inset-0.5 rounded-full bg-sidebar-primary/20 blur-sm -z-10"></div>
                          </div>
                          <div className="grid flex-1 text-left leading-tight ml-2">
                        <span className="truncate font-semibold text-sidebar-foreground text-base">
                          {user?.name || "User"}
                        </span>
                          </div>
                          <ChevronDown className="ml-auto size-5 text-sidebar-muted-foreground group-hover:text-sidebar-accent-foreground transition-colors duration-200" />
                        </SidebarMenuButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                          className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg bg-sidebar border-sidebar-border shadow-elegant backdrop-blur-sm"
                          side="bottom"
                          align="end"
                          sideOffset={4}
                      >
                        <DropdownMenuItem className="flex items-center hover:bg-sidebar-hover py-3">
                          <User className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <div>
                            <div className="font-semibold text-sidebar-foreground text-base">{user?.name || "User"}</div>
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-sidebar-border" />
                        <DropdownMenuItem
                            onClick={() => navigate("/documentation")}
                            className="flex items-center hover:bg-sidebar-hover transition-colors duration-200 py-2.5 cursor-pointer"
                        >
                          <Settings className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <span className="text-sidebar-foreground text-base">{t("helpCenter")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => navigate("/billing")}
                            className="flex items-center hover:bg-sidebar-hover transition-colors duration-200 py-2.5 cursor-pointer"
                        >
                          <CreditCard className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <span className="text-sidebar-foreground text-base">{t("billing")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => navigate("/account")}
                            className="flex items-center hover:bg-sidebar-hover transition-colors duration-200 py-2.5 cursor-pointer"
                        >
                          <Settings className="mr-3 h-5 w-5 text-sidebar-accent-foreground" />
                          <span className="text-sidebar-foreground text-base">{t("settings")}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={logout}
                            className="text-destructive cursor-pointer hover:text-destructive-foreground hover:bg-destructive/10 transition-colors duration-200 py-2.5"
                        >
                          <LogOut className="mr-3 h-5 w-5" />
                          <span className="text-base">{t("logout")}</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </SidebarMenuItem>
                </SidebarMenu>
            )}
          </SidebarFooter>
          <SidebarRail />
        </Sidebar>
        <SidebarInset>
          <header className="flex mb-4 h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center gap-2 px-4 w-full">
              <SidebarTrigger className="-ml-1 hover:bg-accent rounded-lg transition-colors duration-200" />

              {/* New Project button in the top right */}
              <div className="ml-auto">
                <Button
                    onClick={() => navigate("/onboarding-starter")}
                    className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ease-in-out"
                >
                  <Plus className="h-4 w-4" />
                  New Bot
                </Button>
              </div>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
  );
};