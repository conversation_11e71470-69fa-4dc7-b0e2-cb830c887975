// 通用的聊天组件渲染器
// 针对 EventStream (SSE) 优化，解决流式更新时的滚动跳动问题

import { ChatMessage } from "./ChatCore";
import { createChatStyles, ChatStyleConfig, icons } from "./ChatStyles";

export interface ChatRendererConfig extends ChatStyleConfig {
  assistantName: string;
  logo?: string | null;
  showInput?: boolean;
  suggestedQuestions?: string[];
  isLoading?: boolean;
  inputValue?: string;
  messages: ChatMessage[];
  onSendMessage?: (message: string) => void;
  onSuggestedQuestion?: (question: string) => void;
  onInputChange?: (value: string) => void;
  onKeyDown?: (event: KeyboardEvent) => void;
  showCloseButton?: boolean;
  onClose?: () => void;
}

export class ChatRenderer {
  private config: ChatRendererConfig;
  private styles: ReturnType<typeof createChatStyles>;
  private messagesAreaElement: HTMLElement | null = null;
  
  // 滚动相关的优化属性
  private isUserScrolled: boolean = false; // 用户是否主动滚动离开底部
  private scrollThreshold: number = 100; // 距离底部多少像素认为用户在底部
  private lastScrollHeight: number = 0; // 上一次的滚动高度
  private wasAtBottom: boolean = true; // 上次更新时是否在底部
  
  // EventStream 优化相关
  private streamingMessageElement: HTMLElement | null = null; // 当前正在流式更新的消息元素
  private streamingTextElement: HTMLElement | null = null; // 正在更新的文本元素
  private lastMessageId: string = ''; // 上一条消息的ID（用时间戳模拟）
  private rafId: number | null = null; // requestAnimationFrame ID
  private scrollQueue: (() => void)[] = []; // 滚动队列

  constructor(config: ChatRendererConfig) {
    this.config = config;
    this.styles = createChatStyles(config);
  }

  // 更新配置 - 针对 EventStream 优化
  updateConfig(newConfig: Partial<ChatRendererConfig>) {
    const oldMessages = this.config.messages;
    const oldIsLoading = this.config.isLoading;
    
    this.config = { ...this.config, ...newConfig };
    this.styles = createChatStyles(this.config);
    
    // 检测是否是流式更新场景
    if (newConfig.messages) {
      this.handleStreamUpdate(oldMessages, newConfig.messages, oldIsLoading);
    }
  }

  // 处理流式更新
  private handleStreamUpdate(oldMessages: ChatMessage[], newMessages: ChatMessage[], wasLoading: boolean) {
    const isNewMessage = newMessages.length > oldMessages.length;
    const isStreamingUpdate = newMessages.length === oldMessages.length && 
                             newMessages.length > 0 && 
                             this.isLastMessageUpdated(oldMessages, newMessages);
    
    if (isNewMessage) {
      // 新消息 - 需要重新渲染
      this.streamingMessageElement = null;
      this.streamingTextElement = null;
    } else if (isStreamingUpdate) {
      // 流式更新 - 只更新文本内容，不重新渲染整个消息
      this.updateStreamingContent(newMessages[newMessages.length - 1]);
      return; // 不需要完整重新渲染
    }
    
    // 如果加载状态从 true 变为 false，说明流式更新完成
    if (wasLoading && !this.config.isLoading) {
      this.streamingMessageElement = null;
      this.streamingTextElement = null;
    }
  }

  // 检查最后一条消息是否被更新
  private isLastMessageUpdated(oldMessages: ChatMessage[], newMessages: ChatMessage[]): boolean {
    if (oldMessages.length === 0 || newMessages.length === 0) return false;
    
    const oldLast = oldMessages[oldMessages.length - 1];
    const newLast = newMessages[newMessages.length - 1];
    
    return oldLast.type === newLast.type && 
           oldLast.message !== newLast.message &&
           oldLast.type === 'bot'; // 通常只有bot消息会流式更新
  }

  // 更新流式内容 - 核心优化方法
  private updateStreamingContent(message: ChatMessage) {
    if (!this.streamingTextElement) {
      // 如果没有找到流式文本元素，尝试查找
      this.findStreamingTextElement();
    }
    
    if (this.streamingTextElement) {
      // 记录当前滚动位置
      const shouldStayAtBottom = this.shouldStayAtBottom();
      
      // 更新文本内容
      this.streamingTextElement.innerHTML = this.renderMarkdown(message.message);
      
      // 如果应该保持在底部，则平滑滚动
      if (shouldStayAtBottom) {
        this.queueSmoothScroll();
      }
    }
  }

  // 查找当前正在流式更新的文本元素
  private findStreamingTextElement() {
    if (!this.messagesAreaElement) return;
    
    // 查找最后一个bot消息的文本元素
    const messageContainers = this.messagesAreaElement.querySelectorAll('[data-message-type="bot"]');
    const lastBotMessage = messageContainers[messageContainers.length - 1];
    
    if (lastBotMessage) {
      this.streamingTextElement = lastBotMessage.querySelector('.yybot-message-text') as HTMLElement;
    }
  }

  // 队列化平滑滚动 - 避免频繁滚动
  private queueSmoothScroll() {
    this.scrollQueue.push(() => this.performSmoothScroll());
    
    if (this.rafId === null) {
      this.rafId = requestAnimationFrame(() => this.processScrollQueue());
    }
  }

  // 处理滚动队列
  private processScrollQueue() {
    if (this.scrollQueue.length > 0) {
      // 只执行最后一个滚动操作
      const lastScroll = this.scrollQueue[this.scrollQueue.length - 1];
      this.scrollQueue = [];
      lastScroll();
    }
    this.rafId = null;
  }

  // 执行平滑滚动
  private performSmoothScroll() {
    if (!this.messagesAreaElement || !this.shouldStayAtBottom()) return;
    
    const targetScrollTop = this.messagesAreaElement.scrollHeight - this.messagesAreaElement.clientHeight;
    
    // 使用平滑滚动而不是直接跳转
    if (Math.abs(this.messagesAreaElement.scrollTop - targetScrollTop) > 5) {
      this.messagesAreaElement.scrollTo({
        top: targetScrollTop,
        behavior: 'auto' // 在流式更新时使用 auto 而不是 smooth
      });
    }
    
    this.lastScrollHeight = this.messagesAreaElement.scrollHeight;
  }

  // 判断是否应该保持在底部
  private shouldStayAtBottom(): boolean {
    if (!this.messagesAreaElement) return true;
    
    const { scrollTop, scrollHeight, clientHeight } = this.messagesAreaElement;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    
    // 如果用户没有主动滚动，或者距离底部很近，则应该保持在底部
    return !this.isUserScrolled || distanceFromBottom <= this.scrollThreshold;
  }

  // 处理用户手动滚动
  private handleUserScroll = () => {
    if (!this.messagesAreaElement) return;
    
    const { scrollTop, scrollHeight, clientHeight } = this.messagesAreaElement;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    
    // 更新用户滚动状态
    this.isUserScrolled = distanceFromBottom > this.scrollThreshold;
    
    // 记录当前是否在底部
    this.wasAtBottom = distanceFromBottom <= this.scrollThreshold;
  };

  // 公共滚动方法 - 强制滚动到底部
  public performScroll() {
    this.isUserScrolled = false;
    this.wasAtBottom = true;
    this.performSmoothScroll();
  }

  // 创建 DOM 元素的通用方法
  private createElement<T extends HTMLElement = HTMLElement>(
    tag: string,
    styles?: Partial<CSSStyleDeclaration>,
    attributes?: Record<string, string | boolean>
  ): T {
    const element = document.createElement(tag) as T;

    if (styles) {
      Object.assign(element.style, styles);
    }

    if (attributes) {
      this.setElementAttributes(element, attributes);
    }

    return element;
  }

  private setElementAttributes(element: HTMLElement, attributes: Record<string, string | boolean>): void {
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === "innerHTML") {
        element.innerHTML = value as string;
      } else if (key === "textContent") {
        element.textContent = value as string;
      } else if (key === "disabled") {
        (element as HTMLInputElement | HTMLButtonElement).disabled = value as boolean;
      } else {
        element.setAttribute(key, String(value));
      }
    });
  }

  // 渲染头部
  private renderHeader(): HTMLElement {
    const header = this.createElement("div", this.styles.header);
    const headerContent = this.createElement("div", this.styles.headerContent);

    // 头像
    const avatar = this.createElement("div", this.styles.avatar);
    if (this.config.logo) {
      const logoImg = this.createElement("img", this.styles.avatarImage, {
        src: this.config.logo,
        alt: "Assistant",
      });
      logoImg.onerror = () => {
        avatar.innerHTML = "";
        avatar.textContent = this.config.assistantName.charAt(0);
        Object.assign(avatar.style, this.styles.avatarText);
      };
      avatar.appendChild(logoImg);
    } else {
      avatar.textContent = this.config.assistantName.charAt(0);
      Object.assign(avatar.style, this.styles.avatarText);
    }

    // 头部文字
    const headerText = this.createElement("div", this.styles.headerText);
    const assistantName = this.createElement("h4", this.styles.assistantName, {
      textContent: this.config.assistantName,
    });
    const statusText = this.createElement("p", this.styles.statusText, {
      textContent: "Active now",
    });

    headerText.appendChild(assistantName);
    headerText.appendChild(statusText);

    headerContent.appendChild(avatar);
    headerContent.appendChild(headerText);

    // 关闭按钮（如果需要）
    if (this.config.showCloseButton && this.config.onClose) {
      const closeBtn = this.createElement(
        "button",
        this.styles.widgetCloseButton,
        {
          innerHTML: icons.close,
        }
      );
      closeBtn.addEventListener("click", this.config.onClose);
      headerContent.appendChild(closeBtn);
    }

    header.appendChild(headerContent);
    return header;
  }

  // 渲染单个消息 - 添加标识符以便流式更新
  private renderMessage(message: ChatMessage, messageIndex: number): HTMLElement {
    const messageContainer = this.createElement("div", {
      ...this.styles.messageContainer,
      ...(message.type === "user"
        ? this.styles.messageContainerUser
        : this.styles.messageContainerBot),
    });

    // 添加数据属性用于识别
    messageContainer.setAttribute('data-message-type', message.type);
    messageContainer.setAttribute('data-message-index', messageIndex.toString());

    // Bot 头像
    if (message.type === "bot") {
      const botAvatar = this.renderBotAvatar();
      messageContainer.appendChild(botAvatar);
    }

    // 消息气泡
    const messageBubble = this.createElement("div", {
      ...this.styles.messageBubble,
      ...(message.type === "user"
        ? this.styles.messageBubbleUser
        : this.styles.messageBubbleBot),
    });

    // 消息文本 - 添加类名以便查找
    const messageText = this.createElement("div", {
      ...this.styles.messageText,
    });
    messageText.className = 'yybot-message-text'; // 添加类名

    if (message.type === "bot") {
      messageText.innerHTML = this.renderMarkdown(message.message);
      
      // 如果这是最后一条bot消息，记录文本元素引用
      const isLastBotMessage = messageIndex === this.config.messages.length - 1;
      if (isLastBotMessage && this.config.isLoading) {
        this.streamingTextElement = messageText;
      }
    } else {
      messageText.innerHTML = this.highlightLinks(message.message);
    }

    // 时间戳
    const messageTime = this.createElement("p", this.styles.messageTime, {
      textContent: message.time,
    });

    messageBubble.appendChild(messageText);
    messageBubble.appendChild(messageTime);
    messageContainer.appendChild(messageBubble);

    return messageContainer;
  }

  // 简单的 markdown 渲染
  private renderMarkdown(text: string): string {
    let result = text
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/`(.*?)`/g, "<code>$1</code>")
      .replace(/\n/g, "<br>");
    
    result = this.highlightLinks(result);
    
    return result;
  }

  // 链接高亮处理函数
  private highlightLinks(text: string): string {
    const linkRegex = /(https?:\/\/[^\s<>"{}|\\^`\[\]，。!?！？；：""''（）【】]+)/g;
    const parts = text.split(linkRegex);
    
    let result = '';
    parts.forEach((part) => {
      if (/^https?:\/\//.test(part)) {
        const cleanUrl = part.replace(/[.,;:!?！？。，；：]+$/, '');
        const removedPunctuation = part.slice(cleanUrl.length);
        
        // 添加点击事件处理
        result += `<a href="#" target="_blank" rel="noopener noreferrer" style="color: #1e40af; text-decoration: underline; word-break: break-all;" onmouseover="this.style.color='#1d4ed8'" onmouseout="this.style.color='#1e40af'" onclick="event.preventDefault(); window.handleTrackedLinkClick && window.handleTrackedLinkClick('${cleanUrl}', this)"> ${cleanUrl}</a>${removedPunctuation}`;
      } else {
        result += part;
      }
    });
    
    return result;
  }

  // 渲染等待消息气泡
  private renderLoadingMessage(): HTMLElement {
    const messageContainer = this.createElement("div", this.styles.loadingMessageContainer);

    const botAvatar = this.renderBotAvatar();
    messageContainer.appendChild(botAvatar);

    const messageBubble = this.createElement("div", this.styles.loadingMessageBubble);

    const loadingDots = this.createElement("div", this.styles.loadingDots);

    for (let i = 0; i < 3; i++) {
      const dot = this.createElement("div", {
        ...this.styles.loadingDot,
        animationDelay: `${i * 0.2}s`,
      });
      loadingDots.appendChild(dot);
    }

    const loadingText = this.createElement("span", this.styles.loadingText, {
      textContent: "Thinking",
    });

    messageBubble.appendChild(loadingText);
    messageBubble.appendChild(loadingDots);
    messageContainer.appendChild(messageBubble);

    return messageContainer;
  }

  // 渲染机器人头像
  private renderBotAvatar(): HTMLElement {
    const botAvatar = this.createElement("div", this.styles.botAvatar);
    
    if (this.config.logo) {
      const logoImg = this.createElement("img", {
        width: '100%',
        height: '100%',
        borderRadius: '50%',
        objectFit: 'cover'
      }, {
        src: this.config.logo,
        alt: "Assistant",
      });
      
      logoImg.onerror = () => {
        botAvatar.innerHTML = '';
        const fallbackText = this.createElement("span", {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100%',
          fontSize: '10px',
          fontWeight: '500',
          color: 'white'
        }, {
          textContent: this.config.assistantName.charAt(0)
        });
        botAvatar.appendChild(fallbackText);
      };
      
      botAvatar.appendChild(logoImg);
    } else {
      const avatarText = this.createElement("span", this.styles.botAvatarText, {
        textContent: this.config.assistantName.charAt(0),
      });
      botAvatar.appendChild(avatarText);
    }
    
    return botAvatar;
  }

  // 渲染建议问题
  private renderSuggestedQuestions(): HTMLElement {
    const container = this.createElement(
      "div",
      this.styles.suggestedQuestionsContainer
    );

    this.config.suggestedQuestions?.forEach((question) => {
      if (question.trim()) {
        const questionBtn = this.createElement(
          "button",
          {
            ...this.styles.suggestedQuestion,
            ...(this.config.isLoading ? this.styles.suggestedQuestionDisabled : {}),
          },
          {
            textContent: question,
          }
        ) as HTMLButtonElement;

        questionBtn.disabled = !!this.config.isLoading;

        questionBtn.addEventListener("mouseover", () => {
          if (!this.config.isLoading) {
            Object.assign(questionBtn.style, this.styles.suggestedQuestionHover);
          }
        });

        questionBtn.addEventListener("mouseout", () => {
          if (!this.config.isLoading) {
            Object.assign(questionBtn.style, this.styles.suggestedQuestion);
          }
        });

        questionBtn.addEventListener("click", () => {
          if (this.config.onSuggestedQuestion && !this.config.isLoading) {
            this.config.onSuggestedQuestion(question);
          }
        });

        container.appendChild(questionBtn);
      }
    });

    return container;
  }

  // 渲染输入区域
  private renderInputArea(): HTMLElement {
    const inputArea = this.createElement("div", this.styles.inputArea);
    const inputContainer = this.createElement("div", this.styles.inputContainer);

    const input = this.createElement(
      "input",
      {
        ...this.styles.input,
        ...(this.config.isLoading ? this.styles.inputDisabled : {}),
      },
      {
        type: "text",
        placeholder: "Type your message...",
        value: this.config.inputValue || "",
      }
    ) as HTMLInputElement;

    input.disabled = !!this.config.isLoading;

    input.addEventListener("input", (e) => {
      if (this.config.onInputChange) {
        this.config.onInputChange((e.target as HTMLInputElement).value);
      }
    });

    input.addEventListener("keydown", (e) => {
      if (this.config.onKeyDown) {
        this.config.onKeyDown(e);
      }
    });

    const sendBtn = this.createElement(
      "button",
      {
        ...this.styles.sendButton,
        ...(this.config.isLoading ? this.styles.sendButtonDisabled : {}),
      },
      {
        innerHTML: icons.send,
      }
    ) as HTMLButtonElement;

    sendBtn.disabled = !!this.config.isLoading;

    sendBtn.addEventListener("mouseover", () => {
      if (!this.config.isLoading) {
        Object.assign(sendBtn.style, this.styles.sendButtonHover);
      }
    });

    sendBtn.addEventListener("mouseout", () => {
      Object.assign(sendBtn.style, this.styles.sendButton);
    });

    sendBtn.addEventListener("click", () => {
      if (this.config.onSendMessage && input.value.trim() && !this.config.isLoading) {
        this.config.onSendMessage(input.value.trim());
      }
    });

    inputContainer.appendChild(input);
    inputContainer.appendChild(sendBtn);

    const poweredBy = this.createElement("div", this.styles.poweredBy);
    poweredBy.innerHTML = `
      Powered by 
      <a href="/" style="${Object.entries(this.styles.poweredByLink)
        .map(([k, v]) => `${k.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${v}`)
        .join("; ")}">
         YapYapBot
      </a>
    `;

    inputArea.appendChild(inputContainer);
    inputArea.appendChild(poweredBy);

    return inputArea;
  }

  // 渲染消息区域 - EventStream 优化版本
  private renderMessagesArea(): HTMLElement {
    const messagesArea = this.createElement("div", this.styles.messagesArea);
    this.messagesAreaElement = messagesArea;

    // 添加用户滚动监听
    messagesArea.addEventListener("scroll", this.handleUserScroll, { passive: true });

    const messages = this.config.messages;

    // 渲染所有消息
    messages.forEach((message, index) => {
      const isLastMessage = index === messages.length - 1;
      const isEmptyBotMessage = message.type === 'bot' && !message.message.trim();

      // 跳过空的bot消息（在加载状态时）
      if (this.config.isLoading && isLastMessage && isEmptyBotMessage) {
        return;
      }

      const messageElement = this.renderMessage(message, index);
      messagesArea.appendChild(messageElement);
    });

    // 如果正在加载，显示等待消息
    if (this.config.isLoading) {
      messagesArea.appendChild(this.renderLoadingMessage());
    }

    // 渲染建议问题
    if (
      !this.config.isLoading &&
      this.config.suggestedQuestions &&
      this.config.suggestedQuestions.length > 0 &&
      messages.length <= 2
    ) {
      messagesArea.appendChild(this.renderSuggestedQuestions());
    }

    // 初始滚动到底部
    setTimeout(() => {
      this.performSmoothScroll();
    }, 0);

    return messagesArea;
  }

  // 渲染完整的聊天界面
  render(): HTMLElement {
    const container = this.createElement("div", this.styles.container);

    if (this.config.className) {
      container.className = this.config.className;
    }

    container.appendChild(this.renderHeader());
    container.appendChild(this.renderMessagesArea());

    if (this.config.showInput) {
      container.appendChild(this.renderInputArea());
    }

    return container;
  }

  // 创建 Widget 切换按钮
  createToggleButton(onClick: () => void): HTMLElement {
    const button = this.createElement(
      "button",
      this.styles.widgetToggleButton,
      {
        innerHTML: icons.chat,
      }
    );

    button.addEventListener("mouseover", () => {
      Object.assign(button.style, this.styles.widgetToggleButtonHover);
    });

    button.addEventListener("mouseout", () => {
      const currentDisplay = button.style.display;
      Object.assign(button.style, this.styles.widgetToggleButton);
      if (currentDisplay) {
        button.style.display = currentDisplay;
      }
    });

    button.addEventListener("click", onClick);

    return button;
  }

  // 清理资源
  public cleanup() {
    if (this.rafId !== null) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    
    this.scrollQueue = [];
    
    if (this.messagesAreaElement) {
      this.messagesAreaElement.removeEventListener("scroll", this.handleUserScroll);
    }
    
    this.messagesAreaElement = null;
    this.streamingMessageElement = null;
    this.streamingTextElement = null;
  }
}