import { useState, useEffect, useRef } from "react";
import { Send } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { ChatCore, ChatMessage, ChatConfig } from "./ChatRenderer/ChatCore";
import { getChatApiUrl, getSessionApiUrl } from "@/config";
import { createChatStyles } from "@/components/ChatRenderer/ChatStyles";

interface ChatPreviewProps {
  assistantName?: string;
  welcomeMessage?: string;
  color?: string;
  suggestedQuestions?: string[];
  showInput?: boolean;
  compact?: boolean;
  className?: string;
  logo?: string | null;
  projectId?: string;
  initialSessionId?: string;
  notCreateSession?: boolean;
  trackUrls: string[];
}

// 链接高亮组件
const LinkHighlighter = ({
  text,
  handleLinkClick,
}: {
  text: string;
  handleLinkClick: (url: string, e) => void;
}) => {
  const linkRegex =
    /(https?:\/\/[^\s<>"{}|\\^`\[\]，。!?！？；：""''（）【】]+)/g;
  const parts = text.split(linkRegex);

  return (
    <>
      {parts.map((part, index) => {
        if (/^https?:\/\//.test(part)) {
          const cleanUrl = part.replace(/[.,;:!?！？。，；：]+$/, "");
          const removedPunctuation = part.slice(cleanUrl.length);

          return (
            <span key={index}>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: "#1e40af",
                  textDecoration: "underline",
                  wordBreak: "break-all",
                }}
                onClick={(e) => handleLinkClick(cleanUrl, e)}
                onMouseOver={(e) => {
                  (e.target as HTMLElement).style.color = "#1d4ed8";
                }}
                onMouseOut={(e) => {
                  (e.target as HTMLElement).style.color = "#1e40af";
                }}
              >
                {cleanUrl}
              </a>
              {removedPunctuation}
            </span>
          );
        }
        return part;
      })}
    </>
  );
};

// 自定义 ReactMarkdown 组件，支持链接高亮
const MarkdownWithLinks = ({
  children,
  handleLinkClick,
}: {
  children: string;
  handleLinkClick: (url: string, e) => void;
}) => {
  return (
    <ReactMarkdown
      components={{
        a: ({ href, children, ...props }) => (
          <a
            href="#"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              color: "#1e40af",
              textDecoration: "underline",
              wordBreak: "break-all",
            }}
            onClick={(e) => href && handleLinkClick(href, e)}
            onMouseOver={(e) => {
              (e.target as HTMLElement).style.color = "#1d4ed8";
            }}
            onMouseOut={(e) => {
              (e.target as HTMLElement).style.color = "#1e40af";
            }}
            {...props}
          >
            {children}
          </a>
        ),
        p: ({ children }) => {
          if (typeof children === "string") {
            return (
              <p>
                <LinkHighlighter
                  text={children}
                  handleLinkClick={handleLinkClick}
                />
              </p>
            );
          }
          return <p>{children}</p>;
        },
      }}
    >
      {children}
    </ReactMarkdown>
  );
};

// Bot头像组件
const BotAvatar = ({
  logo,
  assistantName,
  styles,
}: {
  logo: string | null;
  assistantName: string;
  styles: any;
}) => {
  return (
    <div style={styles.botAvatar}>
      {logo ? (
        <img
          src={logo}
          alt="Assistant"
          style={{
            ...styles.botAvatarImage,
            width: "100%",
            height: "100%",
            borderRadius: "50%",
            objectFit: "cover",
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = "none";
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `<span style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; font-size: 10px; font-weight: 500; color: white;">${assistantName.charAt(
                0
              )}</span>`;
            }
          }}
        />
      ) : (
        <span style={styles.botAvatarText}>{assistantName.charAt(0)}</span>
      )}
    </div>
  );
};

export const ChatPreview = ({
  assistantName = "YapYapBot",
  welcomeMessage = "Hi! I'm here to help! What would you like to know?",
  color = "#FF7A51",
  suggestedQuestions = ["I have another question"],
  showInput = true,
  compact = false,
  className = "",
  logo = null,
  projectId = "",
  initialSessionId = "",
  notCreateSession = false,
  trackUrls = [],
}: ChatPreviewProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [_sessionId, setSessionId] = useState(initialSessionId);
  const chatCoreRef = useRef<ChatCore | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesAreaRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部 - 只滚动消息区域，不影响页面
  const scrollToBottom = () => {
    if (messagesEndRef.current && messagesAreaRef.current) {
      // 使用 scrollTop 而不是 scrollIntoView，避免影响页面滚动
      const messagesArea = messagesAreaRef.current;
      messagesArea.scrollTop = messagesArea.scrollHeight;
    }
  };

  // 当消息更新时自动滚动
  useEffect(() => {
    // 使用 setTimeout 确保DOM已更新
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 0);

    return () => clearTimeout(timer);
  }, [messages, isLoading]);

  // 初始化ChatCore
  useEffect(() => {
    const config: ChatConfig = {
      projectId,
      assistantName,
      welcomeMessage,
      color,
      logo,
      suggestedQuestions,
      chatApiUrl: getChatApiUrl(),
      sessionApiUrl: getSessionApiUrl(),
      sessionId: initialSessionId,
      notCreateSession,
      trackUrls,
    };

    const callbacks = {
      onMessageUpdate: setMessages,
      onLoadingChange: setIsLoading,
      onSessionIdChange: setSessionId,
    };

    chatCoreRef.current = new ChatCore(config, callbacks);

    // 清理函数，组件卸载时重置chatCoreRef
    return () => {
      chatCoreRef.current = null;
    };
  }, []); // 仅在组件挂载时初始化一次

  // 更新配置当props改变时
  const prevPropsRef = useRef({
    assistantName,
    welcomeMessage,
    color,
    logo,
    suggestedQuestions: JSON.stringify(suggestedQuestions),
    projectId,
    notCreateSession,
    trackUrls: JSON.stringify(trackUrls),
  });

  useEffect(() => {
    if (chatCoreRef.current) {
      const prevProps = prevPropsRef.current;
      const currentProps = {
        assistantName,
        welcomeMessage,
        color,
        logo,
        suggestedQuestions: JSON.stringify(suggestedQuestions),
        projectId,
        notCreateSession,
        trackUrls: JSON.stringify(trackUrls),
      };

      // 检查props是否真的改变了
      if (JSON.stringify(prevProps) !== JSON.stringify(currentProps)) {
        chatCoreRef.current.updateConfig({
          assistantName,
          welcomeMessage,
          color,
          logo,
          suggestedQuestions,
          projectId,
          notCreateSession,
          trackUrls,
        });

        // 更新ref中的值
        prevPropsRef.current = currentProps;
      }
    }
  });

  const sendTrackMessage = async (
    url: string,
    e?:
      | React.MouseEvent<HTMLButtonElement>
      | React.KeyboardEvent<HTMLInputElement>
  ) => {
    // 阻止事件冒泡和默认行为
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    chatCoreRef.current.handleTrackedLinkClick(url);
  };

  const sendMessage = async (
    e?:
      | React.MouseEvent<HTMLButtonElement>
      | React.KeyboardEvent<HTMLInputElement>
  ) => {
    // 阻止事件冒泡和默认行为
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!chatCoreRef.current || !inputValue.trim()) return;

    const question = inputValue;
    setInputValue("");

    await chatCoreRef.current.sendMessage(question);
  };

  const handleSuggestedQuestion = async (
    question: string,
    e?: React.MouseEvent<HTMLButtonElement>
  ) => {
    // 阻止事件冒泡和默认行为
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!chatCoreRef.current) return;
    await chatCoreRef.current.sendMessage(question);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault(); // 阻止默认的表单提交行为
      sendMessage(e);
    }
  };

  // 创建样式
  const styles = createChatStyles({ color, compact });

  return (
    <div
      className={className}
      style={{
        ...styles.container,
        height: "100%",
        // 确保容器不会影响页面滚动
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Chat Header */}
      <div style={styles.header}>
        <div style={styles.headerContent}>
          <div style={styles.avatar}>
            {logo ? (
              <img
                src={logo}
                alt="Assistant"
                style={styles.avatarImage}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  const parent = target.parentElement;
                  if (parent) {
                    parent.textContent = assistantName.charAt(0);
                    Object.assign(parent.style, styles.avatarText);
                  }
                }}
              />
            ) : (
              <span style={styles.avatarText}>{assistantName.charAt(0)}</span>
            )}
          </div>
          <div style={styles.headerText}>
            <h4 style={styles.assistantName}>{assistantName}</h4>
            <p style={styles.statusText}>Active now</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesAreaRef}
        style={{
          ...styles.messagesArea,
          overflowY: "auto",
          overflowX: "hidden",
          // 重要：确保滚动行为只影响这个容器
          scrollBehavior: "smooth",
          // 防止滚动时影响父级
          position: "relative",
        }}
      >
        {messages.map((msg, index) => {
          // 如果是最后一条消息，且是空的bot消息，且正在加载，则跳过渲染
          const isLastMessage = index === messages.length - 1;
          const isEmptyBotMessage = msg.type === "bot" && !msg.message.trim();

          if (isLoading && isLastMessage && isEmptyBotMessage) {
            return null;
          }

          return (
            <div
              key={msg.id}
              style={{
                ...styles.messageContainer,
                ...(msg.type === "user"
                  ? styles.messageContainerUser
                  : styles.messageContainerBot),
              }}
            >
              {msg.type === "bot" && (
                <BotAvatar
                  logo={logo}
                  assistantName={assistantName}
                  styles={styles}
                />
              )}
              <div
                style={{
                  ...styles.messageBubble,
                  ...(msg.type === "user"
                    ? styles.messageBubbleUser
                    : styles.messageBubbleBot),
                }}
              >
                {msg.type === "bot" ? (
                  <div style={styles.messageText}>
                    <MarkdownWithLinks handleLinkClick={sendTrackMessage}>
                      {String(msg.message || "")}
                    </MarkdownWithLinks>
                  </div>
                ) : (
                  <div style={styles.messageText}>
                    <LinkHighlighter
                      text={msg.message}
                      handleLinkClick={sendTrackMessage}
                    />
                  </div>
                )}
                <p style={styles.messageTime}>{msg.time}</p>
              </div>
            </div>
          );
        })}

        {/* Loading Message */}
        {isLoading && (
          <div style={styles.loadingMessageContainer}>
            <BotAvatar
              logo={logo}
              assistantName={assistantName}
              styles={styles}
            />
            <div style={styles.loadingMessageBubble}>
              <span style={styles.loadingText}>Thinking</span>
              <div style={styles.loadingDots}>
                <div
                  style={{ ...styles.loadingDot, animationDelay: "0s" }}
                ></div>
                <div
                  style={{ ...styles.loadingDot, animationDelay: "0.2s" }}
                ></div>
                <div
                  style={{ ...styles.loadingDot, animationDelay: "0.4s" }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Suggested Questions */}
        {!isLoading &&
          suggestedQuestions.length > 0 &&
          messages.length <= 2 && (
            <div style={styles.suggestedQuestionsContainer}>
              {suggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  type="button" // 明确指定按钮类型
                  onClick={(e) => handleSuggestedQuestion(question, e)}
                  style={styles.suggestedQuestion}
                  onMouseOver={(e) => {
                    Object.assign(
                      (e.target as HTMLElement).style,
                      styles.suggestedQuestionHover
                    );
                  }}
                  onMouseOut={(e) => {
                    Object.assign(
                      (e.target as HTMLElement).style,
                      styles.suggestedQuestion
                    );
                  }}
                >
                  {question}
                </button>
              ))}
            </div>
          )}
        {/* 用于自动滚动的锚点 */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      {showInput && (
        <div style={styles.inputArea}>
          <div style={styles.inputContainer}>
            <input
              type="text"
              placeholder="Type your message..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown} // 使用新的键盘事件处理函数
              style={{
                ...styles.input,
                ...(isLoading ? styles.inputDisabled : {}),
              }}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={sendMessage}
              style={{
                ...styles.sendButton,
                ...(isLoading ? styles.sendButtonDisabled : {}),
              }}
              disabled={isLoading}
              onMouseOver={(e) => {
                if (!isLoading) {
                  // 只修改需要变化的属性，保留其他样式
                  const target = e.target as HTMLElement;
                  Object.keys(styles.sendButtonHover).forEach((key) => {
                    target.style[key] = styles.sendButtonHover[key];
                  });
                }
              }}
              onMouseOut={(e) => {
                if (!isLoading) {
                  // 只重置悬停相关的属性，不是完全覆盖
                  const target = e.target as HTMLElement;
                  Object.keys(styles.sendButtonHover).forEach((key) => {
                    target.style[key] = styles.sendButton[key] || "";
                  });
                }
              }}
            >
              <Send style={{ width: "12px", height: "12px" }} />
            </button>
          </div>
          <div style={styles.poweredBy}>
            Powered by{" "}
            <a
              href="/"
              style={styles.poweredByLink}
              onMouseOver={(e) => {
                Object.assign(
                  (e.target as HTMLElement).style,
                  styles.poweredByLinkHover
                );
              }}
              onMouseOut={(e) => {
                Object.assign(
                  (e.target as HTMLElement).style,
                  styles.poweredByLink
                );
              }}
              onClick={(e) => {
                e.preventDefault();
                window.location.href = "/";
              }}
            >
              <img
                src="/logo.png"
                alt="YapYapBot Logo"
                style={{
                  width: "16px",
                  height: "16px",
                  marginRight: "4px",
                  verticalAlign: "middle",
                  display: "inline-block",
                }}
              />
              YapYapBot
            </a>
          </div>
        </div>
      )}
    </div>
  );
};
